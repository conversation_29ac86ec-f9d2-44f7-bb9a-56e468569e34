import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "math_utils.js" as MMath

Rectangle {
    width: 400
    height: 520
    focus: true 
    color: timeLineManager.timeLineController.theme === "dark" ? "#0F1123" : "#F5F5F5"
    ColumnLayout {
        spacing: 10
        anchors.left: parent.left
        anchors.right: parent.right
        RowLayout {
            Layout.alignment: Qt.AlignCenter
            Layout.fillWidth: true
            spacing: 10
            IconButton {
                width: 30
                icon.source: "qrc:src/assets/playback/previous_frame.png"
                onClicked:  {
                    calendarModel.selectedDates = []
                    calendarModel.selectedHours = []
                    if (calendarModel.monthIndex === 1) {
                        calendarModel.monthIndex = 12
                        calendarModel.yearValue--
                    } else {
                        calendarModel.monthIndex--
                    }
                    calendarModel.updateCalendar()
                }
            }
            Text {
                id: monthLabel
                horizontalAlignment: Text.AlignHCenter
                Layout.fillWidth: true
                text: calendarModel.monthText
                color: timeline_text_off
                font.pixelSize: 18
            }

            IconButton {
                width: 30
                icon.source: "qrc:src/assets/playback/next_frame.png"
                isDisabled: false
                onClicked:  {
                    calendarModel.selectedDates = []
                    calendarModel.selectedHours = []
                    if (calendarModel.monthIndex === 12) {
                        calendarModel.monthIndex = 1
                        calendarModel.yearValue++
                    } else {
                        calendarModel.monthIndex++
                    }
                    calendarModel.updateCalendar()
                }
            }
        }
        GridLayout {
            id: calendarGrid
            columns: 7
            Layout.fillWidth: true
            Layout.fillHeight: true
            Repeater {
                model: calendarModel.weekDays
                delegate: Text {
                    text: modelData
                    Layout.alignment: Qt.AlignCenter
                    font.bold: true
                    font.pixelSize: 14
                    color: timeline_text_off
                }
            }
            Repeater {
                id: dayRepeater
                model: calendarModel.dayList
                delegate: Rectangle {
                    width: 40
                    height: 40
                    Layout.fillWidth: true
                    property bool isToday: (calendarModel.yearValue === new Date().getFullYear() &&
                                            calendarModel.monthIndex === (new Date().getMonth() + 1) &&
                                            modelData.day === (new Date().getDate()).toString())
                    color: getDayColor(modelData)
                    radius: 5
                    border.color: isToday ? timeline_background_on : "transparent"
                    border.width: modelData.day === "" ? 0 : 2

                    Text {
                        id: day
                        anchors.centerIn: parent
                        text: modelData.day
                        font.bold: calendarModel.selectedDates.includes(modelData)
                        color: calendarModel.selectedDates.includes(modelData) ? "white" : timeline_text_off
                    }
                    Rectangle {
                        width: parent.width*0.75
                        height: 2
                        anchors{
                            top: day.bottom
                            left: day.left
                            right: day.right
                        }
                        color: timeline_background_on 
                        visible: modelData.isData 
                    }
                    MouseArea {
                        anchors.fill: parent
                        enabled: modelData.enabled
                        onClicked: {
                            if (modelData.day !== "") {
                                let newSelectedDates = calendarModel.selectedDates.slice()
                                if (ctrlPressed) {
                                    if (newSelectedDates.includes(modelData)) {
                                        if (newSelectedDates[0] !== modelData)
                                        {
                                            // nếu ngày cuối được chọn không trùng ngày min thì xóa ngày cũ đi, push lại ngày được chọn xuống cuối để tính toán fullRangeSort cho chuẩn
                                            // nếu ngày cuối được chọn trùng với ngày min thì bỏ qua không phải thêm lại mảng
                                            newSelectedDates.splice(newSelectedDates.indexOf(modelData), 1)
                                            newSelectedDates.push(modelData)
                                        }
                                    } else {
                                        newSelectedDates.push(modelData)
                                    }
                                } else {
                                    newSelectedDates = [modelData]
                                }
                                calendarModel.updateSelectedDates(newSelectedDates)
                            }
                        }
                    }
                }
            }
        }
        GridLayout {
            id: timeGrid
            columns: 6
            Layout.fillWidth: true

            Repeater {
                id: timeRepeater
                model: calendarModel.hourList
                delegate: Rectangle {
                    // width: 60
                    Layout.fillWidth: true
                    height: 30
                    color: (function(){
                        if (modelData.enabled)
                        {
                            return calendarModel.selectedHours.includes(modelData) ? timeline_background_on : timeline_background_off
                        }
                        else{
                            return "transparent"
                        }
                    })()
                    radius: 5
                    // border.color: modelData.enabled ? timeline_background_on : "transparent"
                    // border.width: 1

                    Text {
                        id: hour
                        anchors.centerIn: parent
                        text: modelData.hour
                        font.bold: calendarModel.selectedHours.includes(modelData)
                        color: calendarModel.selectedHours.includes(modelData) ? "white" : timeline_text_off
                    }
                    Rectangle {
                        width: parent.width*0.75
                        height: 2
                        anchors{
                            top: hour.bottom
                            left: hour.left
                            right: hour.right
                        }
                        color: timeline_background_on 
                        visible: modelData.isData 
                    }
                    MouseArea {
                        anchors.fill: parent
                        enabled: modelData.enabled
                        onClicked: {
                            let newSelectedHours = calendarModel.selectedHours.slice()
                            if (ctrlPressed) {
                                if (newSelectedHours.includes(modelData)) {
                                    if (newSelectedHours[0] !== modelData) {
                                        newSelectedHours.splice(newSelectedHours.indexOf(modelData), 1)
                                        newSelectedHours.push(modelData)
                                    }
                                    
                                } else {
                                    newSelectedHours.push(modelData)
                                }
                            } else {
                                newSelectedHours = [modelData]
                            }
                            calendarModel.updateSelectedHours(newSelectedHours)
                        }
                    }
                }
            }
        }
    }
    property var calendarModel: timeLineManager.timeLineController.calendarModel
    property bool ctrlPressed: false
    readonly property color timeline_background_disabled: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_color_theme_by_key("timeline_background_disabled") : "blue"
    readonly property color timeline_background_off: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_color_theme_by_key("timeline_background_off") : "blue"
    readonly property color timeline_background_on: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_color_theme_by_key("timeline_background_on") : "blue"
    readonly property color timeline_icon: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_color_theme_by_key("timeline_icon") : "blue"
    readonly property color timeline_text_off: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_color_theme_by_key("timeline_text_off") : "blue"
    readonly property color timeline_text_on: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_color_theme_by_key("timeline_text_on") : "blue"
    // Component.onCompleted: timeLineManager.timeLineController.calendarModel.updateCalendar()
    function getDayColor(modelData){
        if (modelData.enabled)
        {
            return calendarModel.selectedDates.includes(modelData) ? timeline_background_on : (modelData.day === "" ? "transparent" : timeline_background_off)
        }
        else {
            return "transparent"
        }
        
    }
    Keys.onPressed: (event) => {
        parent.forceActiveFocus() 
        if (event.key === Qt.Key_Control) ctrlPressed = true
    }

    Keys.onReleased: {
        if (event.key === Qt.Key_Control) ctrlPressed = false
    }
}
