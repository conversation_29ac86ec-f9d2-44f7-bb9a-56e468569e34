import QtQuick 2.15
import QtQuick.Controls
import QtQuick.Layouts
import models 1.0
Rectangle {
    id: root
    width: parent.width
    height: parent.height
    color: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_color_theme_by_key("main_background") : "#F5F5F5"
    ColumnLayout {
        // anchors.fill: parent
        anchors.centerIn: parent
        spacing: 5  // Khoảng cách giữa các layout

        RowLayout {
            id: rowTop
            Layout.preferredWidth:190
            Layout.leftMargin: 5
            Layout.rightMargin: 5
            spacing: 5  // Khoảng cách giữa các nút

            // Button {
            //     icon.source: "qrc:src/assets/images/volume_mute_fill.svg"
            //     icon.width:24
            //     icon.height: 24
                
            //     // Layout.fillWidth: true
            // }
            IconButton {
                icon.source: "qrc:src/assets/images/volume_mute_fill.svg"
                implicitWidth: 30
                implicitHeight: 30
                onClicked:  {
                    console.log("volume_mute_fill ",timeLineManager.timeLineController.isSync)
                }
            }
            Slider {
                id: slider
                from: 0            // Giá trị nhỏ nhất
                to: 100            // Giá trị lớn nhất
                value: 50          // Giá trị mặc định
                stepSize: 1        // Bước nhảy
                width: 200         // Chiều rộng Slider
                Layout.fillWidth: true
                onValueChanged: {
                    console.log("Slider Value: " + value)
                }
            }
        }
        RowLayout {
            spacing: 5  // Khoảng cách giữa các nút
            Layout.preferredHeight:30
            Layout.preferredWidth:190
            Layout.leftMargin: 5
            Layout.rightMargin: 5
            ControlButton {
                text: "Live"
                height: 30
                Layout.fillWidth: true
                Layout.fillHeight: true
                buttonType: timeLineManager.timeLineController.isLive
                onClicked:  {
                    if (!timeLineManager.timeLineController.isLive){
                        timeLineManager.timeLineController.isLive = !timeLineManager.timeLineController.isLive
                        timeLineManager.timeLineController.isNextChunk = false
                        timeLineManager.timeLineController.isNextFrame = false
                        timeLineManager.timeLineController.nextFrame = SpeedStatus.Up1X
                    }
                    
                }
            }

            // ControlButton {
            //     text: "Sync"
            //     height: 30
            //     Layout.fillWidth: true
            //     Layout.fillHeight: true
            //     buttonType: timeLineManager.timeLineController.isSync
            //     onClicked:  {
            //         console.log("Sync ",timeLineManager.timeLineController.isSync)
            //     }
            // }
            IconButton {
                height: 30
                Layout.fillWidth: true
                Layout.fillHeight: true
                icon.source: "qrc:src/assets/playback/calendar_playback.png"
                buttonType: timeLineManager.timeLineController.isCalendar
                onClicked:  {
                    onClicked: {
                        timeLineManager.timeLineController.openCalendarDialog(timeLineManager.timeLineController)
                        timeLineManager.timeLineController.isCalendar = !timeLineManager.timeLineController.isCalendar
                    }
                }
            }
        }
    }
}