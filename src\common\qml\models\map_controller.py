
from dataclasses import replace
import json
from PySide6.QtCore import QObject, Property, Signal,Slot,QByteArray, QEnum, QAbstractListModel, Qt, QModelIndex
from src.common.model.camera_model import CameraModel,camera_model_manager,Camera
from src.common.controller.main_controller import main_controller
from src.common.threads.sub_thread import SubThread
import enum
from src.common.model.main_tree_view_model import TreeType
from enum import IntEnum
import pickle
import time
from PySide6.QtCore import QThread,Qt
import copy
import logging
logger = logging.getLogger(__name__)
class CameraListModel(QAbstractListModel):
    ObjectRole = Qt.UserRole + 1
    def __init__(self, parent=None):
        super().__init__(parent)
        self._items = []

    def rowCount(self, parent=QModelIndex()):
        return len(self._items)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or not (0 <= index.row() < len(self._items)):
            return None

        if role == self.ObjectRole:
            return self._items[index.row()]
        return None

    def roleNames(self):
        return {
            self.ObjectRole: b"model"
        }

    def appendCamera(self, camera):
        self.beginInsertRows(QModelIndex(), len(self._items), len(self._items))
        self._items.append(camera)
        self.endInsertRows()

    def removeCamera(self, camera):
        for idx, item in enumerate(self._items):
            if item.id == camera.id:
                self.beginRemoveRows(QModelIndex(), idx, idx)
                self._items.pop(idx)
                self.endRemoveRows()
                return

    def clear(self):
        self.beginResetModel()
        self._items.clear()
        self.endResetModel()

    def getItems(self):
        return self._items
    
    def replaceCamera(self, index, new_camera):
        if 0 <= index < len(self._items):
            self._items[index] = new_camera
class MapState(QObject):
    class ViewMode(IntEnum):
        NORMAL = 0
        FULLSCREEN = 1
    QEnum(ViewMode)

    class LockMode(IntEnum):
        UNLOCK = 0
        LOCK = 1
    QEnum(LockMode)

    class EditMode(IntEnum):
        READ_ONLY = 0
        EDITABLE = 1
    QEnum(EditMode)

    class notifyKey(IntEnum):
        SelectGridType = 0
        SelectEditMode = 1
        MapSavedSuccessfully = 2
        MapSaveFailed = 3
        LocationAlert = 4

    QEnum(notifyKey)

    viewModeChanged = Signal()
    editModeChanged = Signal()
    lockModeChanged = Signal()
    notifyChanged = Signal(int)
    saveDataSignal = Signal()
    dropEventChanged = Signal(QByteArray)

    def __init__(self):
        super().__init__()
        self._editMode = False
        self._viewMode = False
        self._lockMode = True

    @Property(bool,notify=editModeChanged)
    def editMode(self):
        return self._editMode
    
    @editMode.setter
    def editMode(self, value: bool):
        if self._editMode != value:
            self._editMode = value
            if self._editMode:
                self.lockMode = False
            self.editModeChanged.emit()

    @Property(bool,notify=lockModeChanged)
    def lockMode(self):
        return self._lockMode
    
    @lockMode.setter
    def lockMode(self, value: bool):
        if self._lockMode != value:
            self._lockMode = value
            self.lockModeChanged.emit()

    @Property(bool,notify=viewModeChanged)
    def viewMode(self):
        return self._viewMode
    
    @viewMode.setter
    def viewMode(self, value: bool):
        if self._viewMode != value:
            self._viewMode = value
            self.viewModeChanged.emit()

    @Slot(str, result=str)
    def get_color_theme_by_key(self, key=None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Color", key)   
class SizeCameraItem(enum.Enum):
    SMALL = 1
    MEDIUM = 2
    LARGE = 3

class FovModeItem(enum.Enum):
    ICON = 'ICON'
    RECTANGLE = 'RECTANGLE'
    CIRCLE = 'CIRCLE'
    POLYGON = 'POLYGON'

class TypeCameraItem(enum.Enum):
    PTZ = 0
    BULLET = 1
    DOME = 2

class CameraColor(enum.Enum):
    RED = "#B5122E"
    ORANGE = "#D08900"
    BLUE = "#5B5B9F"
    YELLOW = "#FEC502"
    GREEN = "#1CD1A1"
class FloorModel(QObject):
    nameChanged = Signal()
    fileLinkChanged = Signal()
    listCamerasChanged  = Signal()
    updateCameraFloorTreeview = Signal(QObject, list, QObject)
    def __init__(self,data: dict):
        super().__init__()
        self.data = data
        self.id = None
        self.serverIp = None
        self._name = None
        self._fileLink = None
        self._cameras = []
        self.buildingModel = None
        self.synchronize_floor_number = 0
        self._current_new_list_cameras = []
        self.load_data()

    def set_building_model(self, buildingModel):
        self.buildingModel = buildingModel

    def load_data(self):
        if self.data is not None:
            self.id = self.data.get("id",None)
            self.serverIp = self.data.get("serverIp",None)
            self._name = self.data.get("name",None)
            self._fileLink = self.data.get("fileLink",None)
            list_cameraIds = self.data.get("cameraIds",None)
            for camera_id in list_cameraIds:
                camera_model = camera_model_manager.get_camera_model(id = camera_id)
                self._cameras.append(camera_model.data)

    def replace_data(self, data):
        if data is not None:
            self.id = data.get("id",None)
            self.serverIp = data.get("serverIp",None)
            self._name = data.get("name",None)
            self._fileLink = data.get("fileLink",None)
            self._cameras = []
            list_cameraIds = data.get("cameraIds",None)
            for camera_id in list_cameraIds:
                camera_model = camera_model_manager.get_camera_model(id = camera_id)
                self._cameras.append(camera_model.data)

    @Property(str,notify=nameChanged)
    def name(self):
        return self._name
    
    @name.setter
    def name(self, value: str):
        if self._name != value:
            self._name = value
            self.nameChanged.emit() 

    @Property(str,notify=fileLinkChanged)
    def fileLink(self):
        return self._fileLink
    
    @fileLink.setter
    def fileLink(self, value: str):
        if self._fileLink != value:
            self._fileLink = value
            self.fileLinkChanged.emit() 

    def getCameras(self):
        new_list = []
        for item in self._cameras:
            new_list.append(item)
        return new_list
    
    def get_camera_models(self):
        return self._cameras

    cameras = Property(list, getCameras, notify=listCamerasChanged)
    
    def to_dict(self):
        list_id = []
        for item in self._cameras:
            list_id.append(item.get("id",None))

        return {
            "id": self.id,
            "serverIp": self.serverIp,
            "name": self._name,
            "fileLink": self._fileLink,
            "cameraIds": list_id,
        }
    @Slot(str, result=str)
    def get_color_theme_by_key(self, key=None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Color", key)
    
    def update_camera_on_treeview(self):
        self.updateCameraFloorTreeview.emit(self, self._current_new_list_cameras, None)

    def update_list_cameras(self, new_list_cameras):
        self._cameras = new_list_cameras
        self.listCamerasChanged.emit()

    def process_remove_camera_on_floor(self, cameras, floor_destination = None):
        list_cam_ids = []
        for camera in cameras:
            list_cam_ids.append(camera["id"] if isinstance(camera,dict) else camera.id)
        new_list_cameras = []
        for current_camera in self._cameras:
            if current_camera.get("id",None) in list_cam_ids:
                continue
            new_list_cameras.append(current_camera)
        self.updateCameraFloorTreeview.emit(self, new_list_cameras, floor_destination)
class FloorManager(QObject):
    __instance = None
    addFloorList = Signal(tuple)
    def __init__(self):
        super().__init__()
        self.data = {}
        self.addFloorList.connect(self.addFloorListSignal)

    @staticmethod
    def get_instance():
        if FloorManager.__instance is None:
            FloorManager.__instance = FloorManager()
        return FloorManager.__instance
    
    def addFloorListSignal(self, data):
        data, controller = data
        for item in data:
            floor_model = FloorModel(data=item)
            floor_model.serverIp = controller.server.data.server_ip
            floor_manager.add_floor(floor_model)

    def add_floor(self, floor_model: FloorModel):
        if floor_model.serverIp in self.data:
            self.data[floor_model.serverIp][floor_model.id] = floor_model
        else:
            self.data[floor_model.serverIp] = {}
            self.data[floor_model.serverIp][floor_model.id] = floor_model

    def modify_floor_camera_list(self, floor_model: FloorModel):
        if floor_model.serverIp in self.data:
            self.data[floor_model.serverIp][floor_model.id]._cameras = floor_model._cameras
            self.data[floor_model.serverIp][floor_model.id].listCamerasChanged.emit()


    def remove_floor(self, floor_model: FloorModel):
        if floor_model.serverIp in self.data:
            if floor_model.id in self.data[floor_model.serverIp]:
                del self.data[floor_model.serverIp][floor_model.id]

    def get_floor(self,id = None, cameraModel = None,camera_id = None):
        if id is not None:
            for serverIp,item in self.data.items():
                if id in item:
                    return item[id]
            return None
        if cameraModel is not None:
            if cameraModel.data.server_ip in self.data:
                for floor_id,floorModel in self.data[cameraModel.data.server_ip].items():
                    if cameraModel in floorModel._cameras:
                        return floorModel
            return None
        if camera_id is not None:
            temp = []
            for serverIp,listFloors in self.data.items():
                for floor_id,floorModel in listFloors.items():
                    for cameraModel in floorModel._cameras:
                        if camera_id == cameraModel.get("id",None):
                            temp.append(floorModel)
                            break
            return temp
    def to_dict(self):
        dist = {}
        for serverIp, list_floor in self.data.items():
            temp = {}
            for id, floor_model in list_floor.items():
                temp[floor_model.id] = floor_model.to_dict()
            dist[serverIp] = temp
        return dist 
    
    def delete_server(self,serverIp = None):
        if serverIp in self.data:
            del self.data[serverIp]

floor_manager = FloorManager.get_instance()  

class BuildingModel(QObject):
    nameChanged = Signal()
    longitudeChanged = Signal()
    latitudeChanged = Signal()
    locationChanged = Signal()
    floorIdsChanged = Signal()

    def __init__(self,data: dict):
        super().__init__()
        self.data = data
        self.id = None
        self.serverIp = None
        self._name = None
        self._longitude = None
        self._latitude = None
        self._location = None
        self._floorIds = []
        self.buildingModel = None
        self.load_data()

    def load_data(self):
        if self.data is not None:
            self.id = self.data.get("id",None)
            self.serverIp = self.data.get("serverIp",None)
            self._name = self.data.get("name",None)
            self._longitude = self.data.get("longitude",None)
            self._latitude = self.data.get("latitude",None)
            self._location = self.data.get("location",None)
            self.get_floors()

    def resgiterOriginBuildingModel(self, buildingModel):
        self.buildingModel = buildingModel
        self.buildingModel.floorIdsChanged.connect(self.syncfloorIdsFromOrigin)

    def syncfloorIdsFromOrigin(self):
        if self.buildingModel is not None:
            self._floorIds.clear()
            for idx,floorModel in enumerate(self.buildingModel.floorIds):
                self._floorIds.append(floorModel)
            self.floorIdsChanged.emit()

    def get_floors(self):
        list_id = self.data.get("floorIds",[])   
        for floor_id in list_id:
            floor_model = floor_manager.get_floor(id = floor_id)
            floor_model.set_building_model(self)
            self.appendfloorIds(floor_model)

    @Property(str,notify=nameChanged)
    def name(self):
        return self._name
    
    @name.setter
    def name(self, value: str):
        if self._name != value:
            self._name = value
            self.nameChanged.emit() 

    @Property(float,notify=longitudeChanged)
    def longitude(self):
        return self._longitude
    
    @longitude.setter
    def longitude(self, value: float):
        if self._longitude != value:
            self._longitude = value
            self.longitudeChanged.emit() 

    @Property(float,notify=latitudeChanged)
    def latitude(self):
        return self._latitude
    
    @latitude.setter
    def latitude(self, value: float):
        if self._latitude != value:
            self._latitude = value
            self.latitudeChanged.emit() 

    @Property(float,notify=locationChanged)
    def location(self):
        return self._location
    
    @location.setter
    def location(self, value: float):
        if self._location != value:
            self._location = value
            self.locationChanged.emit() 

    def getfloorIds(self):
        return self._floorIds

    def appendfloorIds(self, item):
        if isinstance(item, FloorModel):
            self._floorIds.append(item)
            floor_manager.add_floor(item)
            self.floorIdsChanged.emit()  # Thông báo danh sách đã thay đổi

    def removeFloor(self, item = None,controller = None):
        if item is None:
            for floor_model in self._floorIds:
                if controller is not None:
                    subThread = SubThread(parent=self,target=controller.delete_floor,args=(floor_model.id,))
                    subThread.start()
                floor_manager.remove_floor(floor_model=floor_model)
            self._floorIds = []  
            self.floorIdsChanged.emit() 
        if isinstance(item, FloorModel):
            self._floorIds.remove(item)
            if controller is not None:
                subThread = SubThread(parent=self,target=controller.delete_floor,args=(item.id,))
                subThread.start()
            floor_manager.remove_floor(item)
            self.floorIdsChanged.emit()  # Thông báo danh sách đã thay đổi

    def clearfloorIds(self):
        self._floorIds.clear()
        self.floorIdsChanged.emit()

    floorIds = Property(list, getfloorIds, notify=floorIdsChanged)

    def to_dict(self):
        list_id = []
        for item in self.floorIds:
            list_id.append(item.id)
        return {
            "id": self.id,
            # "serverIp": self.serverIp,
            "name": self._name,
            "latitude": self._latitude,
            "longitude": self._longitude,
            "location": self._location,
            "floorIds": list_id,
        }
class BuildingManager(QObject):
    addBuildingList = Signal(tuple)
    __instance = None
    def __init__(self):
        super().__init__()
        self.data = {}
        self.addBuildingList.connect(self.addBuildingListSignal)


    @staticmethod
    def get_instance():
        if BuildingManager.__instance is None:
            BuildingManager.__instance = BuildingManager()
        return BuildingManager.__instance
    
    def addBuildingListSignal(self,data):
        data,controller = data
        for item in data:
            building_model = BuildingModel(data=item)
            building_model.serverIp = controller.server.data.server_ip
            building_manager.add_building(building_model)

    def add_building(self, building_model: BuildingModel):
        if building_model.serverIp in self.data:
            self.data[building_model.serverIp][building_model.id] = building_model
        else:
            self.data[building_model.serverIp] = {}
            self.data[building_model.serverIp][building_model.id] = building_model

    def get_building(self,id):
        for serverIp,item in self.data.items():
            if id in item:
                return item[id]
            
    def remove_building(self, building_model: BuildingModel):
        if building_model.serverIp in self.data:
            if building_model.id in self.data[building_model.serverIp]:
                building_model.removeFloor()
                del self.data[building_model.serverIp][building_model.id]

    def remove_floor(self, floor_model: FloorModel, controller = None):
        for serverIp, list_building in self.data.items():
            for id, building_model in list_building.items():
                if floor_model in building_model.floorIds:
                    building_model.removeFloor(item = floor_model,controller = controller)
                    return

    def to_dict(self):
        dist = {}
        for serverIp, list_building in self.data.items():
            temp = {}
            for id, building_model in list_building.items():
                temp[building_model.id] = building_model.to_dict()
            dist[serverIp] = temp
        return dist 
    
    def get_building_list(self,serverIp = None):
        if serverIp in self.data:
            return self.data[serverIp]
        else:
            return {}
        
    def delete_server(self,serverIp = None):
        if serverIp in self.data:
            del self.data[serverIp]

building_manager = BuildingManager.get_instance() 

class MapModel(QObject):
    idChanged = Signal()
    cameraIdsChanged = Signal()
    buildingIdsChanged = Signal()
    newCameraChanged = Signal(QObject)
    removeCameraChanged = Signal(QObject)
    removeListCameraChanged = Signal(str,list)
    addCameraChanged = Signal(QObject)
    addBuildingChanged = Signal(QObject)
    removeBuildingChanged = Signal(QObject)
    clearCameraIdsChanged = Signal(str)
    notifyChanged = Signal(int)
    
    def __init__(self,data: dict = None,controller = None):
        super().__init__()
        self.data = data
        self._id = None
        self.serverIp = None
        self._name = None
        self._buildingIds = []
        self._cameraIds = CameraListModel(parent=self)
        self._previousCameraLocation = {}
        self.cloned_model = None
        self.controller = controller
        self.listCameraIdsRemoved = []
        self.load_data()


    def load_data(self):
        if self.data is not None:
            self._id = self.data.get("id",None)
            if self.controller is not None:
                self.serverIp = self.controller.server.data.server_ip
            self._name = self.data.get("name",None)
            self.get_buildings()
            self.get_cameras()
            
    def get_buildings(self):
        list_buildings = building_manager.get_building_list(serverIp=self.serverIp)
        for id, buildingModel in list_buildings.items():
            self.appendBuildingIds(buildingModel)

    def get_cameras(self): 
        list_cameras = camera_model_manager.get_camera_list(server_ip=self.serverIp)
        for id, cameraModel in list_cameras.items():
            if cameraModel.get_property("coordinateLat") is not None:
                self._cameraIds.appendCamera(cameraModel)

    @Property(str,notify=idChanged)
    def id(self):
        return self._id
    
    @id.setter
    def id(self, value: str):
        if self._id != value:
            self._id = value
            self.idChanged.emit() 

    def getCameraIds(self):
        return self._cameraIds

    def appendCameraIds(self, item):
        if isinstance(item, CameraModel):
            for camera in self._cameraIds.getItems():
                if item.id == camera.id:
                    logger.info(f'appendCameraIds {camera.name}')
                    camera.latitude = item.latitude
                    camera.longitude = item.longitude
                    return
            self._cameraIds.appendCamera(item)
            self.addCameraChanged.emit(item)

    def removeCamera(self, item):
        if isinstance(item, CameraModel):
            self._cameraIds.removeCamera(item)
            self.removeCameraChanged.emit(item)

    def clearCameraIds(self):
        self._cameraIds.clear()
        
    @Property(QObject, constant=True)
    def cameraIds(self):
        return self._cameraIds

    def getbuildingIds(self):
        return self._buildingIds

    def appendBuildingIds(self, item):
        if isinstance(item, BuildingModel):
            self._buildingIds.append(item)
            self.buildingIdsChanged.emit()  # Thông báo danh sách đã thay đổi
            
    
    def removeBuilding(self, item):
        if isinstance(item, BuildingModel):
            self._buildingIds.remove(item)
            self.removeBuildingChanged.emit(item)  # Thông báo danh sách đã thay đổi
    
    def clearbuildingIds(self):
        for item in self._buildingIds:
            item.latitude = None
            item.longitude = None

    buildingIds = Property(list, getbuildingIds, notify=buildingIdsChanged)

    def addBuilding(self,buildingModel:BuildingModel):
        self.appendBuildingIds(buildingModel)

    def camera_available(self, camera_id = None):
        for cameraModel in self._cameraIds.getItems():
            if camera_id == cameraModel.id:
                return True
        return False
    
    @Slot(QByteArray,float,float,result=QObject)
    def handleDrop(self, data, latitude, longitude):
        if(data.isEmpty()):
            return
        try:
            data = pickle.loads(data.data())
            logger.info(f"handleDrop = {data}")
        except:
            data = bytes(data.data()).decode('utf-8')
            data = json.loads(data)
        id = data.get('id',None)
        tree_type = data.get('tree_type',None)
        if tree_type == TreeType.Camera:
            model:CameraModel = camera_model_manager.get_camera_model(id = id)
            # camModel = CameraModel(camera=Camera(id=model.id,name=model.name, address=model.data.address,coordinateLat=model.latitude,coordinateLong=model.longitude,server_ip=model.data.server_ip,state=model.data.state))
            cameraData = {
                "id": model.id,
                "name": model.name,
                "address": model.get_property("address",None),
                "server_ip": model.get_property("server_ip",None),
                "coordinateLat": model.get_property("coordinateLat",None),
                "coordinateLong": model.get_property("coordinateLong",None),
                "state": model.get_property("state",None)
            }
            camModel = CameraModel(camera=cameraData)
            if model.get_property('coordinateLat',None) is not None:
                logger.info(f"handleDrop = {model.get_property('coordinateLat',None)}")
                for item in self._cameraIds.getItems():
                    if item.id == camModel.id:
                        self._previousCameraLocation[id] = (item.latitude, item.longitude)
                        camModel.latitude = latitude
                        camModel.longitude = longitude
                        self.appendCameraIds(camModel)
                        return
            else:
                for item in self._cameraIds.getItems():
                    if item.id == camModel.id:
                        item.latitude = latitude
                        item.longitude = longitude
                        return
                camModel.latitude = latitude
                camModel.longitude = longitude
                self.appendCameraIds(camModel)

        elif tree_type == TreeType.BuildingItem:
            model:BuildingModel = building_manager.get_building(id = id)
            buildingModel = BuildingModel(model.to_dict())
            buildingModel.resgiterOriginBuildingModel(model)
            for item in self._buildingIds:
                if item.id == buildingModel.id:
                    item.latitude = latitude
                    item.longitude = longitude
                    return
            buildingModel.latitude = latitude
            buildingModel.longitude = longitude
            self.appendBuildingIds(buildingModel)
            return model
        
    @Slot(str,bool)
    def handleConfirmLocation(self, id, isConfirm):
        logger.info(f'handleConfirmLocation = {id,isConfirm}')
        if not isConfirm:
            latitude, longitude = self._previousCameraLocation[id]
            model:CameraModel = camera_model_manager.get_camera_model(id = id)
            # camModel = CameraModel(camera=Camera(id=model.id,name=model.name, address=model.data.address,coordinateLat=model.latitude,coordinateLong=model.longitude,server_ip=model.data.server_ip))
            cameraData = {
                "id": model.id,
                "name": model.name,
                "address": model.get_property("address",None),
                "server_ip": model.get_property("server_ip",None),
                "coordinateLat": model.get_property("coordinateLat",None),
                "coordinateLong": model.get_property("coordinateLong",None),
                "state": model.get_property("state",None)
            }
            camModel = CameraModel(camera=cameraData)
            camModel.latitude = latitude
            camModel.longitude = longitude
            self.appendCameraIds(camModel)
        
        self._previousCameraLocation.pop(id)

    @Slot(str, result=bool)
    def isPositionChanged(self, id):
        logger.info(f'isPositionChanged = {id,self._previousCameraLocation}')
        return id in self._previousCameraLocation

    @Slot(QObject)    
    def removeCameraFromMap(self,cameraModel:CameraModel) -> None:
        if isinstance(cameraModel, CameraModel):
            cameraModel.latitude = None
            cameraModel.longitude = None
            self.removeCamera(cameraModel)

    @Slot(QObject)    
    def removeBuildingFromMap(self,buildingModel:QObject) -> None:
        if isinstance(buildingModel, BuildingModel):
            buildingModel.latitude = None
            buildingModel.longitude = None

    def to_dict(self):
        list_Building_id = []
        list_cameras_id = []
        for item in self.buildingIds:
            list_Building_id.append(item.id)
        for item in self._cameraIds.getItems():
            list_cameras_id.append(item.id)
        return {
            "id": self._id,
            "serverIp": self.serverIp,
            "name": self._name,
            "buildingIds": list_Building_id,
            "cameraIds": list_cameras_id,
        }

    def resgiterOriginmapModel(self, mapModel):
        self.mapModel = mapModel
        self.mapModel.addCameraChanged.connect(self.syncAddCameraChanged)
        self.mapModel.removeBuildingChanged.connect(self.syncRemoveBuildingChanged)

    def syncAddCameraChanged(self):
        pass

    def syncRemoveBuildingChanged(self,item: BuildingModel):
        if self.mapModel is not None:
            for buildingModel in self._buildingIds:
                if buildingModel.id == item.id:
                    self._buildingIds.remove(buildingModel)
                    self.buildingIdsChanged.emit()

    def clone(self):
        from copy import deepcopy
        start_time = time.time()
        temp = self.to_dict()
        cloned_data = deepcopy(temp)
        cloned_model = MapModel(data = cloned_data,controller=self.controller)
        for idx,building in enumerate(self._buildingIds):
            copyBuildingModel = BuildingModel(data=building.to_dict())
            copyBuildingModel.resgiterOriginBuildingModel(building)
            cloned_model._buildingIds[idx] = copyBuildingModel
        for idx,camera in enumerate(self._cameraIds.getItems()):
            # cameraData = Camera(id = camera.id,name=camera.name, address=camera.data.address,server_ip=camera.data.server_ip,state = camera.data.state)
            cameraData = {
                "id": camera.get_property("id",None),
                "name": camera.get_property("name",None),
                "address": camera.get_property("address",None),
                "server_ip": camera.get_property("server_ip",None),
                "state": camera.get_property("state",None)
            }
            cameraModel = CameraModel(camera=cameraData)
            cameraModel.latitude = camera.latitude
            cameraModel.longitude = camera.longitude
            cloned_model._cameraIds.replaceCamera(idx,cameraModel)
        end_time = time.time()
        cloned_model.resgiterOriginmapModel(self)
        return cloned_model

    def merge(self,new_map):
        # Đồng bộ dữ liệu building sau khi lưu
        self.clearbuildingIds()
        for building in new_map.getbuildingIds():
            building_model:BuildingModel = building_manager.get_building(id = building.id)
            if building_model is None:
                pass
            else:
                def make_thread(building_model, building):
                    def callback(data):
                        if data is not None:
                            building_model.latitude = data[0]["latitude"]
                            building_model.longitude = data[0]["longitude"]
                    def process():
                        listCameras = []
                        for floorModel in building_model._floorIds:
                            for camera in floorModel._cameras:
                                camera["coordinateLat"] = building.latitude
                                camera["coordinateLong"] = building.longitude
                                if not new_map.camera_available(camera_id = camera["id"]):
                                    listCameras.append(camera)
                        result = self.controller.update_list_cameras_by_put(data=listCameras) 
                        if result is not None:
                            pass
                        data = {"id": building_model.id,"name": building.name, "latitude": building.latitude,"longitude": building.longitude,"location": building.location}
                        result = self.controller.update_building_by_put([data])  
                        if result is not None:
                            return result
                    subThread = SubThread(parent=self,target=process,callback=callback)
                    subThread.start() 
                make_thread(building_model,building)
        # Đồng bộ dữ liệu camera đã bị xóa khỏi bản đồ số sau khi lưu
        originMap = self.to_dict()
        newMap = new_map.to_dict()
        list_id = []
        for id in originMap["cameraIds"]:
            if id not in newMap["cameraIds"]:
                list_id.append(id)
        list_cameras = []
        for cameraModel in self._cameraIds.getItems():
            if cameraModel.id in list_id:
                data = cameraModel.data
                data['coordinateLat'] = None
                data['coordinateLong'] = None
                list_cameras.append(data)

        def callback(data):
            if data is not None:
                dist = []
                for item in list_cameras:
                    cameraModel:CameraModel = camera_model_manager.get_camera_model(id = item["id"])
                    if cameraModel is not None:
                        dist.append(cameraModel)
                        cameraModel.latitude = None
                        cameraModel.longitude = None
                self.removeListCameraChanged.emit(self.serverIp,dist)
        def process():
            if len(list_cameras)>0:
                result = self.controller.update_list_cameras_by_put(list_cameras)   
                return result 
                
        subThread = SubThread(parent=self,target=process,callback = callback)
        subThread.start() 

        # Đồng bộ dữ liệu camera sau khi lưu
        # self.clearCameraIds()
        for camera in new_map._cameraIds.getItems():
            cameraModel:CameraModel = camera_model_manager.get_camera_model(id=camera.id)
            listFloors = floor_manager.get_floor(camera_id=cameraModel.id)
            for floorModel in listFloors:
                floorModel.process_remove_camera_on_floor([cameraModel.data])

            def make_thread(cameraModel, camera):
                def callback(data):
                    if data is not None:
                        cameraModel.latitude = camera.latitude
                        cameraModel.longitude = camera.longitude
                        # cameraModel.data.mapId = self.id
                        self.appendCameraIds(cameraModel)
                def process():
                    temp = CameraModel(camera=copy.deepcopy(cameraModel.data))
                    temp.set_property("coordinateLat",camera.latitude)
                    temp.set_property("coordinateLong",camera.longitude)
                    result = self.controller.update_list_cameras_by_put([temp.data])
                    if result is not None:
                        return result
                subThread = SubThread(parent=self,target=process,callback=callback)
                subThread.start() 
            make_thread(cameraModel,camera)  

    def process_remove_camera_on_map(self, cameraId: str):
        for cameraModel in self._cameraIds.getItems():
            if cameraId == cameraModel.id:
                self.removeCamera(cameraModel)

class MapManager(QObject):
    addMapList = Signal(tuple)
    __instance = None
    def __init__(self):
        super().__init__()
        self.data = {}
        self.addMapList.connect(self.addMapListSignal)

    @staticmethod
    def get_instance():
        if MapManager.__instance is None:
            MapManager.__instance = MapManager()
        return MapManager.__instance
    
    def addMapListSignal(self,data):
        data,controller = data
        for item in data:
            map_model = MapModel(data = item, controller = controller)
            map_manager.add_map(map_model)

    def add_map(self, map_model: MapModel):
        self.data[map_model.serverIp] = map_model

    def add_building(self, building_model: BuildingModel):
        if building_model.serverIp in self.data:
            map_model:MapModel = self.data[building_model.serverIp]
            map_model.appendBuildingIds(building_model)
            building_manager.add_building(building_model)

    def remove_building(self, building_model: BuildingModel):
        if building_model.serverIp in self.data:
            map_model:MapModel = self.data[building_model.serverIp]
            if building_model in map_model._buildingIds:
                map_model.removeBuilding(building_model)
                building_manager.remove_building(building_model)

    def remove_camera(self, cameraModel: CameraModel):
        if cameraModel.serverIp in self.data:
            map_model:MapModel = self.data[cameraModel.serverIp]
            if cameraModel in map_model._cameraIds.getItems():
                map_model.removeCamera(cameraModel)

    def get_map_model(self, serverIp = None, id = None,camera_id = None):
        if serverIp in self.data:
            return self.data[serverIp]
        for i, map_model in self.data.items():
            if id == map_model.id:
                return map_model
        if camera_id is not None:
            for serverIp,mapModel in self.data.items():
                for cameraModel in mapModel.cameraIds.getItems():
                    if cameraModel.id == camera_id:
                        return mapModel
        return None
    
    def to_dict(self):
        dist = {}
        for k, v in self.data.items():
            dist[k] = v.to_dict()
        return dist 
    
    def delete_server(self,serverIp = None):
        if serverIp in self.data:
            del self.data[serverIp]

map_manager = MapManager.get_instance()



class Map2DController(QObject):
    floorModelChanged = Signal()
    listCameraChanged = Signal()
    mapStateChanged = Signal()
    saveCameraListSignal = Signal()
    saveCameraStatusSignal = Signal(bool)
    themeChangeSignal = Signal()

    def __init__(self, floor_model: FloorModel, map_state: MapState = None):
        super().__init__()
        self._floorModel = floor_model
        self._tempListCameras = self.cloneCameraList(self._floorModel._cameras)
        self._mapState = map_state
        self.saveCameraListSignal.connect(self.saveCameraData)
        main_controller.theme_change_signal.connect(self.themeChangeSignal)

    @Property(QObject,notify=floorModelChanged)
    def floorModel(self):
        return self._floorModel
    
    @floorModel.setter
    def floorModel(self, value: QObject):
        if self._floorModel != value:
            self._floorModel = value
            self.floorModelChanged.emit() 

    @Property(QObject,notify=mapStateChanged)
    def mapState(self):
        return self._mapState
    
    @mapState.setter
    def mapState(self, value: QObject):
        if self._mapState != value:
            self._mapState = value
            self.mapStateChanged.emit() 

    @Property(list,notify=listCameraChanged)
    def listCameras(self):
        return self._tempListCameras
    
    @listCameras.setter
    def listCameras(self, value: list):
        if self._tempListCameras != value:
            self._tempListCameras = value
            self.listCameraChanged.emit()

    @Slot(int)
    def temporarilyDeleteCamera(self, index):
        self._tempListCameras.pop(index)
        self.listCameraChanged.emit()

    @Slot(QByteArray, float, float, float)
    def temporarilyAddCamera(self, data: QByteArray, relative_x, relative_y, ratio):
        if(data.isEmpty()):
            return
        try:
            data = pickle.loads(data.data())
        except:
            data = bytes(data.data()).decode('utf-8')
            data = json.loads(data)

        id = data.get('id',None)
        camera_model:CameraModel = camera_model_manager.get_camera_model(id = id)
        if len(floor_manager.get_floor(camera_id = id)) > 0:
            main_controller.show_message_dialog(None, "CAMERA_ALREADY_HAVE_LOCATION")
        if camera_model is not None:
            for camera in self._tempListCameras:
                if camera['id'] == camera_model.id:
                    main_controller.show_message_dialog(None, "CAMERA_EXIST_IN_MAP")
                    return
            fov_data = {
                'position': f"{relative_x},{relative_y}",
                'arc_start_angle': -45,
                'arc_range_angle': 90,
                'radius': 80 / ratio,
                'icon_type': 0
            }
            # camera_data = camera_model.data
            camera_model.set_property("coordinateLat",self._floorModel.buildingModel._latitude)
            camera_model.set_property("coordinateLong",self._floorModel.buildingModel._longitude)
            camera_model.set_property("fovData",json.dumps(fov_data))
            camera_model.set_property("color",CameraColor.BLUE.value)
            camera_model.set_property("fovEnable",True)
            camera_model.set_property("fovMode",FovModeItem.ICON.value)
            camera_model.set_property("nameEnable",True)
            camera_model.set_property("size",SizeCameraItem.MEDIUM.value)

            self._tempListCameras.append(camera_model.data)
            self.listCameraChanged.emit()

    @Slot(int, dict)
    def modifyTempCameraList(self, index, data):
        self._tempListCameras[index] = data

    @Slot(str, result=str)
    def get_color_theme_by_key(self, key = None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Color", key)

    @Slot()
    def saveCameraData(self):
        new_list_cameras = [camera for camera in self._tempListCameras]
        removable_camera_floor = {}
        self._floorModel._current_new_list_cameras = new_list_cameras
        for camera in new_list_cameras:
            mapModel:MapModel = map_manager.get_map_model(camera_id = camera["id"])
            if mapModel is not None:
                logger.info(f"saveCameraData")
                mapModel.process_remove_camera_on_map(camera["id"])
            if camera["floorId"] != self._floorModel.id and camera["floorId"] is not None:
                if camera["floorId"] not in removable_camera_floor:
                    removable_camera_floor[camera["floorId"]] = []
                removable_camera_floor[camera["floorId"]].append(camera)
        self._floorModel.synchronize_floor_number = len(removable_camera_floor)
        for floorId, list_camera in removable_camera_floor.items():
            floorModel:FloorModel = floor_manager.get_floor(id = floorId)
            if floorModel is not None:
                floorModel.process_remove_camera_on_floor(list_camera, floor_destination = self._floorModel)
        if self._floorModel.synchronize_floor_number == 0:
            self._floorModel.update_camera_on_treeview()
        main_controller.show_message_dialog(None, "SAVE_FLOOR")
        self.floorModelChanged.emit()
    
    def cloneCameraList(self, listCameras):
        new_list_cam = []
        for idx,camera in enumerate(listCameras):
            dict_camera = camera
            new_list_cam.append(dict_camera)

        return new_list_cam
    
    def clearlistCameras(self):
        self._floorModel._cameras.clear()
