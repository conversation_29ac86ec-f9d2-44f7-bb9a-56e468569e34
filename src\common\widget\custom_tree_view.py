import json
import logging
import pickle
from src.common.controller.main_controller import main_controller
from src.common.qml.models.map_controller import MapModel,BuildingModel,FloorModel
from src.common.model.camera_model import CameraModel
from src.common.model.group_model import GroupModel
logger = logging.getLogger(__name__)
from PySide6.QtCore import Qt, QEvent, Signal
from PySide6.QtGui import QStandardItemModel, QStandardItem, QCursor, QAction
from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QTreeView,
    QMenu,
    QFrame,
    QHeaderView,
    QStyledItemDelegate,
    QLineEdit,
    QApplication, QStyle, QToolTip
)
from typing import List
from src.styles.style import Style


class EditDelegate(QStyledItemDelegate):

    def __init__(self, parent=None, model_data=None, callback_setModelData=None):
        super().__init__(parent)
        self.parent1 = parent
        self.model_data = model_data
        self.callback_setModelData = callback_setModelData
        # self.hovered_item = None

    def createEditor(self, parent, option, index):
        # hủy đăng ký lắng nghe phím tắt khi show Dialog
        if main_controller.key_filter is not None:
            QApplication.instance().removeEventFilter(main_controller.key_filter)
        editor = QLineEdit(parent)
        editor.setStyleSheet(f"background-color: {Style.PrimaryColor.background};color:#FFFFFF;")
        return editor

    def setEditorData(self, editor, index):
        text = index.model().data(index, Qt.DisplayRole)
        editor.setText(text)

    def setModelData(self, editor, model, index):
        # đăng ký lắng nghe phím tắt khi show Dialog
        if self.callback_setModelData is not None:
            self.callback_setModelData(editor, model, index)
        if main_controller.key_filter is not None:
            QApplication.instance().installEventFilter(main_controller.key_filter)

class EditableTreeView(QTreeView):
    def __init__(self, parent=None, model_data=None, callback_keyPressEvent=None):
        super().__init__(parent)
        self.setEditTriggers(QTreeView.EditTrigger.NoEditTriggers)
        # self.setFocusPolicy(Qt.NoFocus)
        self.model_data = model_data
        self.callback_keyPressEvent = callback_keyPressEvent

    def keyPressEvent(self, event):
        key = event.key()
        if key == Qt.Key.Key_Up or key == Qt.Key.Key_Down or key == Qt.Key.Key_Right or key == Qt.Key.Key_Left:
            event.ignore()  # Ngăn chặn xử lý sự kiện phím mũi tên lên xuống
        else:
            super().keyPressEvent(event)

class CustomStandardItemModel(QStandardItemModel):
    def mimeData(self, indexes):
        if len(indexes) == 1:
            mime_data = super(CustomStandardItemModel, self).mimeData(indexes)
            if indexes:
                standard_item = self.itemFromIndex(indexes[0])
                tree_type = indexes[0].data(Qt.UserRole)  # Get tree type (Camera/Group etc)
                
                print(f"🔄 DEBUG: Creating mime data for single item:")
                print(f"- Item text: {indexes[0].data()}")
                print(f"- Tree type: {tree_type}")
                
                # Set text data
                mime_data.setText(indexes[0].data())
                
                # Set format type
                mime_data.setData("text/plain", indexes[0].data().encode())
                
                if standard_item.item_model is not None:
                    # Set object type and data
                    if isinstance(standard_item.item_model,CameraModel) or isinstance(standard_item.item_model,GroupModel) or isinstance(standard_item.item_model,FloorModel) or isinstance(standard_item.item_model,BuildingModel) or isinstance(standard_item.item_model,MapModel):
                        data = {
                            'id': standard_item.item_model.id,
                            'tree_type': tree_type,
                            'name': standard_item.text()
                        }
                        
                        print(f"✅ DEBUG: Setting mime data for item model:")
                        print(f"- ID: {standard_item.item_model.id}")
                        print(f"- Name: {standard_item.text()}")
                        
                        # Set application/json format
                        mime_data.setData('application/json', json.dumps(data).encode())
                        
                        # Set object type as format
                        mime_data.setData(f'application/{tree_type.lower()}', b'1')
                        # Set custom data format
                        mime_data.setData('application/x-qabstractitemmodeldatalist', 
                            json.dumps({
                                'id': standard_item.item_model.id,
                                'type': tree_type,
                                'data': standard_item.item_model.data
                            }).encode()
                        )
                    else:
                        data = {
                            'id': standard_item.item_model.data.id,
                            'tree_type': tree_type,
                            'name': standard_item.text()
                        }
                        
                        print(f"✅ DEBUG: Setting mime data for item model:")
                        print(f"- ID: {standard_item.item_model.data.id}")
                        print(f"- Name: {standard_item.text()}")
                        
                        # Set application/json format
                        mime_data.setData('application/json', json.dumps(data).encode())
                        
                        # Set object type as format
                        mime_data.setData(f'application/{tree_type.lower()}', b'1')
                        
                        print(f"✅ DEBUG: Added item to mime data:")
                        print(f"- ID: {standard_item.item_model.data.id}")
                        print(f"- Type: {tree_type}")
                        print(f"- data: {standard_item.item_model.data.__dict__}")

                        # Set custom data format
                        mime_data.setData('application/x-qabstractitemmodeldatalist', 
                            json.dumps({
                                'id': standard_item.item_model.data.id,
                                'type': tree_type,
                                'data': standard_item.item_model.data.__dict__
                            }).encode()
                        )

        else:
            # Handle multiple selection
            print("🔄 DEBUG: Creating mime data for multiple items")
            mime_data = super(CustomStandardItemModel, self).mimeData(indexes)
            data = {}
            
            for index in indexes:
                if index.isValid():
                    standard_item = self.itemFromIndex(index)
                    if standard_item.item_model is not None:
                        if isinstance(standard_item.item_model,CameraModel) or isinstance(standard_item.item_model,GroupModel):
                            data[standard_item.item_model.id] = {
                                'type': index.data(Qt.UserRole),
                                'name': standard_item.text()
                            }
                        else:
                            data[standard_item.item_model.data.id] = {
                                'type': index.data(Qt.UserRole),
                                'name': standard_item.text()
                            }

            
            # Set multi-selection data
            mime_data.setText('Multi_selection_item')
            mime_data.setData('application/json', json.dumps(data).encode())
            mime_data.setData('application/multi-selection', b'1')
            
            print(f"✅ DEBUG: Multi-selection data created with {len(data)} items")

        return mime_data

class TreeViewBase(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)

        self.item_selected_list: List[QStandardItem] = []
        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(self.layout)

        self.tree_view = EditableTreeView()
        # self.tree_view.setAlternatingRowColors(True)
        # self.tree_view.setFrameStyle(QFrame.NoFrame)

        # Fix horizontal scrollbar not showing correctly size
        self.tree_view.header().setStretchLastSection(False)
        self.tree_view.header().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        # Fix horizontal scrollbar not showing correctly size

        self.tree_view.setStyleSheet(
            f"""
                    QTreeView {{
                        background-color: {main_controller.get_theme_attribute('Color', 'main_background')};
                        alternate-background-color: {main_controller.get_theme_attribute('Color', 'main_background')};
                        border: None;
                        color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')};
                    }}
                    QTreeView::item {{
                        padding: 4px;
                        color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')};
                    }}
                    QTreeView::item::selected {{
                        background-color: {main_controller.get_theme_attribute('Color', 'main_background')};
                        color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')};
                    }}
                    QTreeView::branch:has-children:closed {{
                        image: url({main_controller.get_theme_attribute('Image', 'treeview_expand_item')});
                    }}
                    QTreeView::branch:has-children:open {{
                        image: url({main_controller.get_theme_attribute('Image', 'treeview_collapse_item')});
                    }}
                    QHeaderView::section {{
                        background-color: {main_controller.get_theme_attribute('Color', 'main_background')};
                        color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')};
                    }}
                """)
        scroll_bar_ver = self.tree_view.verticalScrollBar()
        scroll_bar_hor = self.tree_view.horizontalScrollBar()
        scroll_bar_ver.setStyleSheet(Style.StyleSheet.scrollbar_ver_style)
        scroll_bar_hor.setStyleSheet(Style.StyleSheet.scrollbar_hor_style)
        # self.tree_view.setMaximumWidth(500)
        # self.scroll.setWidget(self.tree_view)
        # self.tree_view.setSortingEnabled(True)
        self.tree_view.setFrameShape(QTreeView.Shape.NoFrame)
        self.tree_view.setFrameShadow(QFrame.Shadow.Plain)
        self.tree_view.setAlternatingRowColors(True)
        self.tree_view.setDragEnabled(True)
        self.tree_view.setDragDropMode(QTreeView.DragDropMode.DragOnly)
        self.tree_view.setSelectionMode(QTreeView.SelectionMode.ExtendedSelection)
        self.tree_view.setSelectionBehavior(QTreeView.SelectionBehavior.SelectRows)
        self.tree_view.setDragDropOverwriteMode(True)
        self.tree_view.setDropIndicatorShown(True)
        # Set the initial scrollbar policies
        self.tree_view.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.tree_view.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        # Install event filters
        self.tree_view.installEventFilter(self)
        # QApplication.instance().installEventFilter(self)
        # add data to tree view
        self.model = CustomStandardItemModel()
        self.tree_view.setModel(self.model)
        self.tree_view.setHeaderHidden(True)
        self.root_tree_view = self.model.invisibleRootItem()
        self.tree_view.clicked.connect(self.on_tree_view_clicked)
        self.tree_view.pressed.connect(self.on_tree_view_pressed)
        self.tree_view.doubleClicked.connect(self.on_tree_view_doubleclicked)
        self.tree_view.entered.connect(self.on_tree_view_entered)
        self.tree_view.activated.connect(self.on_tree_view_activated)

        # IMPORTANT: Connect context menu event to custom slot
        self.tree_view.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree_view.customContextMenuRequested.connect(self.show_context_menu)

        # delegate = EditDelegate(model = self.tree_data)
        # self.tree_view.setItemDelegateForColumn(0, delegate)

        self.layout.addWidget(self.tree_view)

    def eventFilter(self, source, event):
        # logger.debug(f'eventFilter11 ')
        if source is self.tree_view:
            
            if event.type() == QEvent.Enter:
                # Show scrollbars when mouse enters QTreeView
                self.tree_view.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
                self.tree_view.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
            elif event.type() == QEvent.Leave:
                # Hide scrollbars when mouse leaves QTreeView
                self.tree_view.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
                self.tree_view.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
            elif event.type() == QEvent.KeyPress:
                pass
                # logger.debug(f'eventFilter22 ')
        return super().eventFilter(source, event)

    def set_model(self, model: CustomStandardItemModel = None):
        self.model = model
        self.tree_view.setModel(model)
        self.tree_view.setHeaderHidden(True)
        self.root_tree_view = self.model.invisibleRootItem()

    def add_item(self, item=None, name=None, tree_type="tree type 1"):
        pass

    def on_tree_view_activated(self, index):
        pass
        # logger.debug('on_tree_view_activated')

    def on_tree_view_entered(self, index):
        # logger.debug('on_tree_view_entered')
        pass

    def on_tree_view_pressed(self, index):
        pass

    def on_tree_view_clicked(self, index):
        pass

    def on_tree_view_doubleclicked(self, index):
        pass

    def show_context_menu(self, pos):
        pass


