import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import models 1.0
// import controllers 1.0
import '../../../common/qml/map'

Rectangle {
    id: root
    color: "transparent"
    border.color: isSelected ? (isDarkTheme ? "#3B82F6" : "#60A5FA") : "transparent"
    border.width: isSelected ? 2 : 1
    visible: opacity > 0
    opacity: gridModel && gridModel.isMaximized ? (gridModel.activeItemPosition === position ? 1 : 0) : 1
    z: calculateZ()
    property var itemData: null


    // Listen for cell dimension changes
    Connections {
        target: gridModel
        function onCellDimensionsChanged(pos, width, height) {
            if (pos === position) {
                var cellWidth = root.parent.width / gridModel.columns;
                var cellHeight = root.parent.height / gridModel.rows;
                root.width = width * cellWidth;
                root.height = height * cellHeight;
            }
        }
    }

    property real normalX: x
    property real normalY: y
    property real normalWidth: width
    property real normalHeight: height
    property real startWidth: 0
    property real startHeight: 0
    property real startX: 0
    property real startY: 0
    property bool isSwapping: false
    property bool dragActive: false
    // Drag and drop properties
    Drag.active: dragArea.drag.active && !(gridModel && gridModel.isMaximized) // Không cho phép kéo thả khi ở chế độ fullscreen
    Drag.hotSpot.x: width / 2
    Drag.hotSpot.y: height / 2
    Drag.source: root

    // Resize areas
    Rectangle {
        id: rightResizer
        width: 4
        height: parent.height
        color: mouseArea.containsMouse || mouseArea.pressed ? (isDarkTheme ? "#3B82F6" : "#60A5FA") : "transparent"
        anchors.right: parent.right
        z: 10

        MouseArea {
            id: mouseArea
            anchors.fill: parent
            anchors.margins: -4 // Tăng vùng phát hiện chuột
            hoverEnabled: true
            cursorShape: Qt.SizeHorCursor
            property real lastMouseX: 0

            onPressed: {
                lastMouseX = mouseX
                startWidth = root.width
            }

            onPositionChanged: {
                if (pressed) {
                    var delta = mouseX - lastMouseX
                    var cellWidth = root.parent.width / gridModel.columns
                    var newWidth = Math.max(cellWidth, root.width + delta)
                    var newWidthCells = Math.round(newWidth / cellWidth)
                    var currentDimensions = gridModel.getCellDimensions(position)

                    // Chỉ thay đổi kích thước nếu không có xung đột
                    if (canResizeToSize(newWidthCells, currentDimensions.height)) {
                        root.width = newWidthCells * cellWidth
                    }
                }
            }

            onReleased: {
                var cellWidth = root.parent.width / gridModel.columns
                var cellHeight = root.parent.height / gridModel.rows
                var widthCells = Math.round(root.width / cellWidth)
                var heightCells = Math.round(root.height / cellHeight)

                // Kiểm tra lại một lần nữa trước khi cập nhật
                if (canResizeToSize(widthCells, heightCells)) {
                    gridModel.updateCellDimensions(position, widthCells, heightCells)
                } else {
                    // Nếu không thể resize, trở về kích thước cũ
                    var currentDimensions = gridModel.getCellDimensions(position)
                    root.width = currentDimensions.width * cellWidth
                    root.height = currentDimensions.height * cellHeight
                }
            }
        }
    }

    Rectangle {
        id: bottomResizer
        width: parent.width
        height: 4
        color: mouseArea2.containsMouse || mouseArea2.pressed ? (isDarkTheme ? "#3B82F6" : "#60A5FA") : "transparent"
        anchors.bottom: parent.bottom
        z: 10

        MouseArea {
            id: mouseArea2
            anchors.fill: parent
            anchors.margins: -4 // Tăng vùng phát hiện chuột
            hoverEnabled: true
            cursorShape: Qt.SizeVerCursor
            property real lastMouseY: 0

            onPressed: {
                lastMouseY = mouseY
                startHeight = root.height
            }

            onPositionChanged: {
                if (pressed) {
                    var delta = mouseY - lastMouseY
                    var cellHeight = root.parent.height / gridModel.rows
                    var newHeight = Math.max(cellHeight, root.height + delta)
                    var newHeightCells = Math.round(newHeight / cellHeight)
                    var currentDimensions = gridModel.getCellDimensions(position)

                    // Chỉ thay đổi kích thước nếu không có xung đột
                    if (canResizeToSize(currentDimensions.width, newHeightCells)) {
                        root.height = newHeightCells * cellHeight
                    }
                }
            }

            onReleased: {
                var cellWidth = root.parent.width / gridModel.columns
                var cellHeight = root.parent.height / gridModel.rows
                var widthCells = Math.round(root.width / cellWidth)
                var heightCells = Math.round(root.height / cellHeight)

                // Kiểm tra lại một lần nữa trước khi cập nhật
                if (canResizeToSize(widthCells, heightCells)) {
                    gridModel.updateCellDimensions(position, widthCells, heightCells)
                } else {
                    // Nếu không thể resize, trở về kích thước cũ
                    var currentDimensions = gridModel.getCellDimensions(position)
                    root.width = currentDimensions.width * cellWidth
                    root.height = currentDimensions.height * cellHeight
                }
            }
        }
    }

    Rectangle {
        id: bottomRightResizer
        width: 8
        height: 8
        color: mouseArea3.containsMouse || mouseArea3.pressed ? (isDarkTheme ? "#3B82F6" : "#60A5FA") : "transparent"
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        z: 10

        MouseArea {
            id: mouseArea3
            anchors.fill: parent
            anchors.margins: -4 // Tăng vùng phát hiện chuột
            hoverEnabled: true
            cursorShape: Qt.SizeFDiagCursor
            property real lastMouseX: 0
            property real lastMouseY: 0

            onPressed: {
                lastMouseX = mouseX
                lastMouseY = mouseY
                startWidth = root.width
                startHeight = root.height
            }

            onPositionChanged: {
                if (pressed) {
                    var deltaX = mouseX - lastMouseX
                    var deltaY = mouseY - lastMouseY

                    var cellWidth = root.parent.width / gridModel.columns
                    var cellHeight = root.parent.height / gridModel.rows

                    var newWidth = Math.max(cellWidth, root.width + deltaX)
                    var newHeight = Math.max(cellHeight, root.height + deltaY)

                    var newWidthCells = Math.round(newWidth / cellWidth)
                    var newHeightCells = Math.round(newHeight / cellHeight)

                    // Chỉ thay đổi kích thước nếu không có xung đột
                    if (canResizeToSize(newWidthCells, newHeightCells)) {
                        root.width = newWidthCells * cellWidth
                        root.height = newHeightCells * cellHeight
                    }
                }
            }

            onReleased: {
                var cellWidth = root.parent.width / gridModel.columns
                var cellHeight = root.parent.height / gridModel.rows
                var widthCells = Math.round(root.width / cellWidth)
                var heightCells = Math.round(root.height / cellHeight)

                // Kiểm tra lại một lần nữa trước khi cập nhật
                if (canResizeToSize(widthCells, heightCells)) {
                    gridModel.updateCellDimensions(position, widthCells, heightCells)
                } else {
                    // Nếu không thể resize, trở về kích thước cũ
                    var currentDimensions = gridModel.getCellDimensions(position)
                    root.width = currentDimensions.width * cellWidth
                    root.height = currentDimensions.height * cellHeight
                }
            }
        }
    }

    // Functions
    function calculateZ() {
        if (gridModel && gridModel.isMaximized) {
            return (position === gridModel.activeItemPosition) ? 3000 : -1;
        } else if (isMaximizing || isResizing) {
            return 2000;
        } else if (dragActive || isSwapping) {
            return 1000;
        } else {
            return 1; // Always return positive z-index for map items
        }
    }

    // Function to handle map movement
    function handleMapMove(dx, dy) {
        // Use the map2DonGrid ID directly
        if (map2DonGrid) {
            // For Map2DonGrid, we need to handle the movement differently than MapOnGrid
            // For now, we'll implement a basic panning mechanism
            map2DonGrid.x += dx
            map2DonGrid.y += dy

            // Ensure the map stays within reasonable bounds
            var minX = -map2DonGrid.width / 2
            var maxX = contentArea.width - map2DonGrid.width / 2
            var minY = -map2DonGrid.height / 2
            var maxY = contentArea.height - map2DonGrid.height / 2

            map2DonGrid.x = Math.max(minX, Math.min(maxX, map2DonGrid.x))
            map2DonGrid.y = Math.max(minY, Math.min(maxY, map2DonGrid.y))

            console.log("Map moved: dx=" + dx + ", dy=" + dy + ", new position: x=" + map2DonGrid.x + ", y=" + map2DonGrid.y)
        } else {
            console.error("Map2DonGrid component not found")
        }
    }
    function updatePosition() {
        var cellWidth = root.parent.width / gridModel.columns
        var cellHeight = root.parent.height / gridModel.rows
        var col = Math.floor(x / cellWidth)
        var row = Math.floor(y / cellHeight)
        var newPos = row * gridModel.columns + col

        if (newPos !== position && newPos >= 0 && newPos < gridModel.columns * gridModel.rows) {
            position = newPos
            x = col * cellWidth
            y = row * cellHeight
        }
    }
    function startResize(type, mouseX, mouseY) {
        if (type === "width") {
            startWidth = root.width;
            startX = mouseX;
        } else if (type === "height") {
            startHeight = root.height;
            startY = mouseY;
        } else {
            startWidth = root.width;
            startHeight = root.height;
            startX = mouseX;
            startY = mouseY;
        }
    }

    function resize(type, mouseX, mouseY) {
        if (type === "width") {
            var delta = mouseX - startX;
            var newWidth = startWidth + delta;
            var newColumns = Math.ceil(newWidth / (root.parent.width / gridModel.columns));
            root.width = newColumns * (root.parent.width / gridModel.columns);
        } else if (type === "height") {
            var delta = mouseY - startY;
            var newHeight = startHeight + delta;
            var newRows = Math.ceil(newHeight / (root.parent.height / gridModel.rows));
            root.height = newRows * (root.parent.height / gridModel.rows);
        } else {
            var deltaX = mouseX - startX;
            var deltaY = mouseY - startY;
            var newColumns = Math.ceil((startWidth + deltaX) / (root.parent.width / gridModel.columns));
            var newRows = Math.ceil((startHeight + deltaY) / (root.parent.height / gridModel.rows));
            root.width = newColumns * (root.parent.width / gridModel.columns);
            root.height = newRows * (root.parent.height / gridModel.rows);
        }
    }
    function onGridItemChanged(){
        border.color = defaultBorderColor
    }
    Component.onDestruction: {
        if (gridModel && gridModel.gridItemSelected && gridModel.gridItemSelected.widget === root){
            console.log("Component.onDestruction ")
            gridModel.gridItemSelected.widget = null
        }
    }

    // Properties
    property bool isDarkTheme: true
    property int position: 0
    property bool isDragging: false
    property bool isHovered: false
    property bool isSelected: false
    property bool isMaximizing: false
    property bool isResizing: false
    property bool isMapMoveMode: false  // New property to track map move mode
    property int lastDragTime: 0
    property int dragCooldown: 200
    property var dragDimensions: gridModel ? gridModel.getCellDimensions(position) : {"width": 1, "height": 1}

    // Lắng nghe sự kiện thay đổi kích thước từ gridModel
    Connections {
        target: gridModel
        function onCellDimensionsChanged(pos, width, height) {
            if (pos === position) {
                var cellWidth = root.parent.width / gridModel.columns;
                var cellHeight = root.parent.height / gridModel.rows;
                root.width = width * cellWidth;
                root.height = height * cellHeight;
            }
        }

        // Lắng nghe tín hiệu restoreAnimationStarted từ grid_manager.py
        function onRestoreAnimationStarted(activePosition) {
            console.log("Received restoreAnimationStarted signal for position:", activePosition)
            if (position === activePosition) {
                console.log("This is the active item, starting restore animation with high z-index")
                // Đặt z-index cao khi bắt đầu animation
                isResizing = true
                root.z = calculateZ()
                console.log("Restore animation started, z-index set to:", root.z)
                // Bắt đầu animation restore
                restoreAnimation.start()
            }
        }
    }
    property var aiSolutions: null
    property var availableTabs: null

    // Border color handling
    property string defaultBorderColor: "transparent"
    property string hoverBorderColor: isDarkTheme ? "#4fd1c5" : "#4fd1c5"
    property string dragBorderColor: "#FF6B6B"

    onIsHoveredChanged: {
        // if (isHovered && camera_id !== "") {
        //     border.color = hoverBorderColor
        // } else {
        //     border.color = defaultBorderColor
        // }
    }

    // Thêm thuộc tính để theo dõi vị trí ban đầu và trạng thái drop
    property bool dropSuccessful: false

    // Update stored normal values when grid changes (but not during maximize)
    onXChanged: {
        if (gridModel && !gridModel.isMaximized && !isSwapping && !dragActive) normalX = x;
    }
    onYChanged: {
        if (gridModel && !gridModel.isMaximized && !isSwapping && !dragActive) normalY = y;
    }
    onWidthChanged: {
        if (gridModel && !gridModel.isMaximized && !isSwapping && !dragActive) normalWidth = width;
    }

    onHeightChanged: {
        if (gridModel && !gridModel.isMaximized && !isSwapping && !dragActive) normalHeight = height
    }
    // Bind position and size based on maximized state
    x: gridModel && gridModel.isMaximized && gridModel.activeItemPosition === position ? 0 : normalX
    y: gridModel && gridModel.isMaximized && gridModel.activeItemPosition === position ? 0 : normalY
    width: gridModel && gridModel.isMaximized && gridModel.activeItemPosition === position ? parent.width : normalWidth
    height: gridModel && gridModel.isMaximized && gridModel.activeItemPosition === position ? parent.height : normalHeight


    Behavior on opacity {
        NumberAnimation { duration: 200 }
    }

    Behavior on x {
        NumberAnimation {
            duration: 300
            easing.type: Easing.InOutQuad
        }
    }

    Behavior on y {
        NumberAnimation {
            duration: 300
            easing.type: Easing.InOutQuad
        }
    }

    Behavior on width {
        NumberAnimation {
            duration: 300
            easing.type: Easing.InOutQuad
        }
    }

    Behavior on height {
        NumberAnimation {
            duration: 300
            easing.type: Easing.InOutQuad
        }
    }

    // Hover effect and drag area
    MouseArea {
        id: dragArea
        anchors.fill: parent
        hoverEnabled: true
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        drag.target: !root.isMapMoveMode && gridModel && !gridModel.isMaximized ? parent : undefined // Only allow grid dragging when not in map move mode
        drag.smoothed: true
        // Pass through mouse events to the map when the item is selected or in map move mode
        propagateComposedEvents: root.isMapMoveMode
        // When in map move mode, we need to disable the mouse area to allow hover events to reach map items
        enabled: !root.isMapMoveMode
        drag.filterChildren: true
        z: 2

        property real dragThreshold: 10
        property real pressX: 0
        property real pressY: 0
        property bool isDragging: false
        property bool isClick: false
        property point lastMapPosition: Qt.point(0, 0)

        onPressed: function(mouse) {
            forceActiveFocus()

            try {
                if(gridModel && gridModel.gridItemSelected && gridModel.gridItemSelected.widget && gridModel.gridItemSelected.widget !== root){
                    if (typeof gridModel.gridItemSelected.widget.onGridItemChanged === "function") {
                        gridModel.gridItemSelected.widget.onGridItemChanged()
                    }
                }

                border.color = hoverBorderColor

                if (gridModel) {
                    var col = position % gridModel.columns
                    var row = Math.floor(position / gridModel.columns)
                    var dimensions = gridModel.getCellDimensions(position)
                    var widthCells = dimensions.width
                    var heightCells = dimensions.height

                    if (!gridModel.isMaximized || position !== gridModel.activeItemPosition) {
                        x = (col * parent.width / gridModel.columns)
                        y = (row * parent.height / gridModel.rows)
                        width = (parent.width / gridModel.columns) * widthCells
                        height = (parent.height / gridModel.rows) * heightCells
                    }
                }

                if (gridModel && gridModel.gridItemSelected) {
                    gridModel.gridItemSelected.widget = root
                }

                // Store initial position for map movement
                if (root.isMapMoveMode) {
                    lastMapPosition = Qt.point(mouse.x, mouse.y)
                }

            } catch (e) {
                console.error("Error in onPressed:", e)
            }

            // Only handle grid movement if not in map move mode and not selected
            if (!root.isMapMoveMode && !isSelected) {
                var currentTime = Date.now()
                var timeSinceLastDrag = currentTime - (root.lastDragTime || 0)

                if (timeSinceLastDrag < root.dragCooldown) {
                    console.log("Press ignored - too soon after last drag:", timeSinceLastDrag, "ms")
                    return
                }

                pressX = mouse.x
                pressY = mouse.y
                startX = root.x
                startY = root.y
                isClick = true
                startWidth = root.width
                startHeight = root.height
            }

            // Propagate the event to the map when selected or in map move mode
            if (isSelected || root.isMapMoveMode) {
                mouse.accepted = false
            }
        }

        onPositionChanged: function(mouse) {
            if (root.isMapMoveMode) {
                // Handle map movement
                if (mouse.buttons & Qt.LeftButton) {
                    var dx = mouse.x - lastMapPosition.x
                    var dy = mouse.y - lastMapPosition.y

                    // Update map position based on mouse movement
                    if (map2DonGrid) {
                        // Call the map movement handler
                        handleMapMove(dx, dy)
                    }

                    lastMapPosition = Qt.point(mouse.x, mouse.y)
                }

                // Propagate the event to the map
                mouse.accepted = false
            } else if (isSelected) {
                // When selected, propagate events to the map
                mouse.accepted = false
            } else {
                // Handle grid movement
                if (isClick && mouse.buttons & Qt.LeftButton) {
                    var dx = mouse.x - pressX
                    var dy = mouse.y - pressY
                    var distance = Math.sqrt(dx * dx + dy * dy)

                    if (distance > dragThreshold && !isDragging && !(gridModel && gridModel.isMaximized)) {
                        isDragging = true
                        isClick = false
                        dragActive = true
                        root.opacity = 0.8
                        root.z = 5000

                        var dimensions = gridModel ? gridModel.getCellDimensions(position) : {"width": 1, "height": 1}
                        root.dragDimensions = dimensions
                        root.Drag.mimeData = {
                            "text/plain": "map",
                            "map/position": position.toString(),
                            "map/width": dimensions.width.toString(),
                            "map/height": dimensions.height.toString(),
                            "map/isFullscreen": (gridModel && gridModel.isMaximized).toString()
                        }
                    }
                }
            }
        }

        onReleased: function(mouse) {
            if (root.isMapMoveMode) {
                // Reset map movement state
                lastMapPosition = Qt.point(0, 0)
                // Propagate the event to the map
                mouse.accepted = false
            } else if (isSelected) {
                // When selected, propagate events to the map
                mouse.accepted = false
            } else {
                // Existing grid movement release logic
                try {
                    root.lastDragTime = Date.now()
                    isDragging = false
                    isClick = false
                    if (dragActive) {
                        // Keep dragActive true until the swap is complete
                        // Only set isSwapping to true to indicate we're in the swap phase
                        isSwapping = true
                        root.opacity = 1

                        // Maintain high z-index during the entire drag and swap process
                        // Don't reset z-index here, it will be reset after the swap is complete
                        console.log("Dragging ended, entering swap phase, maintaining high z-index:", root.z)

                        // Lấy kích thước hiện tại của item
                        var currentDimensions = gridModel ? gridModel.getCellDimensions(position) : {"width": 1, "height": 1}
                        var widthCells = currentDimensions.width
                        var heightCells = currentDimensions.height

                        // Tính toán vị trí grid dựa trên tọa độ thả
                        var dropX = root.x + width / 2
                        var dropY = root.y + height / 2

                        // Tìm item tại vị trí thả
                        var dropItem = null
                        var items = root.parent.activeItems || {}

                        for (var pos in items) {
                            var item = items[pos]
                            if (item && item !== root) {
                                if (dropX >= item.x && dropX <= item.x + item.width &&
                                    dropY >= item.y && dropY <= item.y + item.height) {
                                    dropItem = item
                                    break
                                }
                            }
                        }

                        // Tính toán vị trí ô trong grid
                        var gridCol = gridModel ? Math.floor(dropX / (root.parent.width / gridModel.columns)) : 0
                        var gridRow = gridModel ? Math.floor(dropY / (root.parent.height / gridModel.rows)) : 0

                        // Điều chỉnh vị trí để giữ tâm của camera
                        // Tính toán offset từ tâm của camera đến góc trên bên trái
                        var offsetCol = Math.floor(widthCells / 2)
                        var offsetRow = Math.floor(heightCells / 2)

                        // Điều chỉnh vị trí để giữ tâm của camera
                        var adjustedGridCol = Math.max(0, gridCol - offsetCol)
                        var adjustedGridRow = Math.max(0, gridRow - offsetRow)

                        // Đảm bảo không vượt quá biên phải và biên dưới
                        adjustedGridCol = gridModel ? Math.min(adjustedGridCol, gridModel.columns - widthCells) : 0
                        adjustedGridRow = gridModel ? Math.min(adjustedGridRow, gridModel.rows - heightCells) : 0

                        // Tính vị trí mới trong grid
                        var targetPos = gridModel ? adjustedGridRow * gridModel.columns + adjustedGridCol : 0

                        // Kiểm tra xem vị trí thả có nằm trong grid container không
                        var isInsideGrid = dropX >= 0 && dropX <= root.parent.width &&
                                          dropY >= 0 && dropY <= root.parent.height

                        // Nếu thả ra ngoài grid container hoặc vị trí không hợp lệ, trở về vị trí cũ
                        if (!isInsideGrid || targetPos < 0 || (gridModel && targetPos >= gridModel.columns * gridModel.rows)) {
                            isSwapping = false
                            returnAnimation.start()
                            return
                        }

                        // Nếu thả vào vị trí hợp lệ và trong phạm vi grid
                        if (targetPos >= 0 && gridModel && targetPos < gridModel.columns * gridModel.rows) {
                            // Check if dropping on itself - if so, restore position
                            if (targetPos === position) {
                                root.x = adjustedGridCol * (root.parent.width / gridModel.columns)
                                root.y = adjustedGridRow * (root.parent.height / gridModel.rows)
                                isSwapping = false
                                return
                            }

                            if (!dropItem) {
                                // Nếu thả vào ô trống
                                var oldPos = position

                                // Đã lấy kích thước hiện tại của item ở trên
                                // widthCells và heightCells đã được tính toán

                                // Kiểm tra xem có đủ ô trống liên tiếp không
                                var hasEnoughSpace = true
                                for (var i = 0; i < heightCells; i++) {
                                    for (var j = 0; j < widthCells; j++) {
                                        var checkPos = targetPos + i * gridModel.columns + j
                                        // Kiểm tra vị trí có nằm trong grid không
                                        if (checkPos >= gridModel.columns * gridModel.rows ||
                                            Math.floor(checkPos / gridModel.columns) !== Math.floor((targetPos + i * gridModel.columns) / gridModel.columns)) {
                                            hasEnoughSpace = false
                                            break
                                        }
                                        // Kiểm tra ô có bị chiếm không
                                        for (var pos in root.parent.activeItems) {
                                            if (pos === oldPos.toString()) continue
                                            var item = root.parent.activeItems[pos]
                                            if (!item) continue

                                            var itemDimensions = gridModel ? gridModel.getCellDimensions(parseInt(pos)) : {"width": 1, "height": 1}
                                            var itemStartCol = gridModel ? Math.floor(parseInt(pos) % gridModel.columns) : 0
                                            var itemStartRow = gridModel ? Math.floor(parseInt(pos) / gridModel.columns) : 0
                                            var itemEndCol = itemStartCol + itemDimensions.width - 1
                                            var itemEndRow = itemStartRow + itemDimensions.height - 1

                                            var checkCol = gridModel ? Math.floor(checkPos % gridModel.columns) : 0
                                            var checkRow = gridModel ? Math.floor(checkPos / gridModel.columns) : 0

                                            if (checkCol >= itemStartCol && checkCol <= itemEndCol &&
                                                checkRow >= itemStartRow && checkRow <= itemEndRow) {
                                                hasEnoughSpace = false
                                                break
                                            }
                                        }
                                        if (!hasEnoughSpace) break
                                    }
                                    if (!hasEnoughSpace) break
                                }

                                if (!hasEnoughSpace) {
                                    isSwapping = false
                                    returnAnimation.start()
                                    return
                                }

                                // Cập nhật position cho GridItem
                                position = targetPos

                                // Cập nhật vị trí hiển thị, giữ nguyên kích thước
                                if (gridModel) {
                                    root.width = (root.parent.width / gridModel.columns) * widthCells
                                    root.height = (root.parent.height / gridModel.rows) * heightCells
                                    root.x = adjustedGridCol * (root.parent.width / gridModel.columns)
                                    root.y = adjustedGridRow * (root.parent.height / gridModel.rows)
                                }

                                // Cập nhật trong activeItems - chỉ cập nhật khi vị trí thực sự thay đổi
                                if (oldPos !== targetPos) {
                                    // Lưu trữ item hiện tại
                                    var currentItem = root.parent.activeItems[oldPos]
                                    // Xóa khỏi vị trí cũ
                                    delete root.parent.activeItems[oldPos]
                                    // Thêm vào vị trí mới
                                    root.parent.activeItems[targetPos] = currentItem
                                    // Thông báo cho gridModel
                                    gridModel.updateVideoPosition(oldPos, targetPos)
                                    // Đảm bảo kích thước được giữ nguyên khi di chuyển
                                }

                                isSwapping = false
                                return
                            } else if (dropItem.position !== position) {
                                // Nếu thả vào ô đã có video -> thực hiện swap
                                var sourcePos = position
                                var targetPos = dropItem.position

                                // Get dimensions for both items
                                var sourceDimensions = gridModel ? gridModel.getCellDimensions(sourcePos) : {"width": 1, "height": 1}
                                var targetDimensions = gridModel ? gridModel.getCellDimensions(targetPos) : {"width": 1, "height": 1}

                                // Cập nhật position cho cả hai GridItem
                                position = targetPos
                                dropItem.position = sourcePos

                                // Cập nhật kích thước và vị trí hiển thị cho cả hai GridItem
                                if (gridModel) {
                                    root.width = (root.parent.width / gridModel.columns) * targetDimensions.width
                                    root.height = (root.parent.height / gridModel.rows) * targetDimensions.height
                                    root.x = (targetPos % gridModel.columns) * (root.parent.width / gridModel.columns)
                                    root.y = Math.floor(targetPos / gridModel.columns) * (root.parent.height / gridModel.rows)

                                    dropItem.width = (root.parent.width / gridModel.columns) * sourceDimensions.width
                                    dropItem.height = (root.parent.height / gridModel.rows) * sourceDimensions.height
                                    dropItem.x = (sourcePos % gridModel.columns) * (root.parent.width / gridModel.columns)
                                    dropItem.y = Math.floor(sourcePos / gridModel.columns) * (root.parent.height / gridModel.rows)
                                }

                                // Cập nhật trong activeItems
                                var tempItem = root.parent.activeItems[sourcePos]
                                root.parent.activeItems[sourcePos] = root.parent.activeItems[targetPos]
                                root.parent.activeItems[targetPos] = tempItem

                                // Thông báo cho gridModel
                                if (gridModel) {
                                    gridModel.swapPositions(sourcePos, targetPos)
                                }

                                isSwapping = false
                                return
                            }
                        }

                        // Nếu không thả được vào đâu, trở về vị trí cũ
                        isSwapping = false
                        returnAnimation.start()
                    }

                    // Kiểm tra xem map có nằm trong hàng và cột hợp lệ không
                    // Chỉ kiểm tra khi không ở chế độ maximize và không đang trong quá trình maximize/restore
                    if (gridModel && !isSwapping && !gridModel.isMaximized && !isMaximizing && !isResizing) {
                        var finalGridCol = Math.floor(root.x / (root.parent.width / gridModel.columns))
                        var finalGridRow = Math.floor(root.y / (root.parent.height / gridModel.rows))
                        var finalCellWidth = root.parent.width / gridModel.columns
                        var finalCellHeight = root.parent.height / gridModel.rows

                        // Kiểm tra xem camera có nằm đúng vị trí của ô trong grid không
                        var isAlignedWithGrid = Math.abs(root.x - finalGridCol * finalCellWidth) < 5 &&
                                               Math.abs(root.y - finalGridRow * finalCellHeight) < 5

                        if (!isAlignedWithGrid ||
                            finalGridCol < 0 || finalGridCol >= gridModel.columns ||
                            finalGridRow < 0 || finalGridRow >= gridModel.rows) {
                            console.log("Map is not aligned with grid or outside valid row/column. Col:", finalGridCol,
                                       "Row:", finalGridRow, "isAligned:", isAlignedWithGrid)
                            returnAnimation.start()
                        }
                    }

                } catch (e) {
                    console.error("Error in onReleased:", e)
                    // Đảm bảo reset các trạng thái quan trọng
                    isDragging = false
                    root.opacity = 1

                    // Keep dragActive and isSwapping true during the return animation
                    // to maintain high z-index

                    // Trở về vị trí ban đầu - z-index will be reset when animation completes
                    returnAnimation.start()
                } finally {
                    // Đảm bảo opacity được reset
                    isDragging = false
                    root.opacity = 1

                    // Chỉ reset các trạng thái và z-index nếu không đang trong animation
                    if (!returnAnimation.running && !dropAnimation.running) {
                        // Đảm bảo z-index được đặt lại trong mọi trường hợp
                        if (!isMaximizing && !isResizing) {
                            dragActive = false
                            isSwapping = false
                            root.z = calculateZ()
                            console.log("Finally block: z-index reset to:", root.z)
                        }
                    }
                }
            }
        }

        onEntered: function(mouse) {
            // console.log("Mouse entered map at position:", position);
            root.isHovered = true
        }

        onExited: function(mouse) {
            // console.log("Mouse exited map at position:", position);
            checkHoverTimer.start()
        }
    }

    // Chỉ giữ lại returnAnimation để trở về vị trí
    ParallelAnimation {
        id: returnAnimation

        NumberAnimation {
            target: root
            property: "x"
            to: startX
            duration: 300
            easing.type: Easing.OutQuad
        }

        NumberAnimation {
            target: root
            property: "y"
            to: startY
            duration: 300
            easing.type: Easing.OutQuad
        }

        onStarted: {
            // Đặt z-index cao khi bắt đầu animation
            root.z = 5000
            console.log("Return animation started, z-index set to:", root.z)
        }

        onFinished: {
            // Đặt lại z-index về giá trị bình thường khi kết thúc
            dragActive = false
            isSwapping = false
            root.z = calculateZ()
            console.log("Return animation completed, z-index reset to:", root.z)
        }
    }

    // Thêm animation fit vào ô target
    ParallelAnimation {
        id: dropAnimation

        NumberAnimation {
            target: root
            property: "x"
            duration: 150
            easing.type: Easing.OutQuad
        }

        NumberAnimation {
            target: root
            property: "y"
            duration: 150
            easing.type: Easing.OutQuad
        }

        NumberAnimation {
            target: root
            property: "width"
            duration: 150
            easing.type: Easing.OutQuad
        }

        NumberAnimation {
            target: root
            property: "height"
            duration: 150
            easing.type: Easing.OutQuad
        }

        onStarted: {
            // Đặt z-index cao khi bắt đầu animation
            root.z = 5000
            console.log("Drop animation started, z-index set to:", root.z)
        }

        onFinished: {
            // Sau khi animation hoàn tất, trở về vị trí ban đầu
            // Không reset z-index ở đây vì returnAnimation sẽ tiếp tục giữ z-index cao
            // z-index sẽ được reset khi returnAnimation kết thúc
            returnAnimation.start()
        }
    }

    // Drop area
    DropArea {
        id: itemDropArea
        anchors.fill: parent
        keys: ["videoItem"]
        enabled: !(gridModel && gridModel.isMaximized) // Không cho phép drop khi ở chế độ fullscreen

        // Thêm thuộc tính để kiểm tra phần trăm chồng lấp
        property real overlapThreshold: 0.5 // 50% overlap required

        onEntered: function(drag) {
            if (drag.source && drag.source !== root) {
                // Kiểm tra phần trăm chồng lấp
                var sourceItem = drag.source
                var targetItem = root

                // Tính toán vùng chồng lấp
                var sourceRect = Qt.rect(sourceItem.x, sourceItem.y, sourceItem.width, sourceItem.height)
                var targetRect = Qt.rect(targetItem.x, targetItem.y, targetItem.width, targetItem.height)

                var overlapRect = calculateOverlapRect(sourceRect, targetRect)
                var overlapArea = overlapRect.width * overlapRect.height
                var sourceArea = sourceItem.width * sourceItem.height

                var overlapPercentage = overlapArea / sourceArea

                // Chỉ hiển thị viền khi đủ phần trăm chồng lấp
                if (overlapPercentage >= overlapThreshold) {
                    root.border.color = dragBorderColor
                }
            }
        }

        onExited: {
            if (isHovered && camera_id !== "") {
                root.border.color = hoverBorderColor
            } else {
                root.border.color = defaultBorderColor
            }
        }

        onDropped: function(drop) {
            // Empty handler
        }

        // Hàm tính toán vùng chồng lấp giữa hai hình chữ nhật
        function calculateOverlapRect(rect1, rect2) {
            var left = Math.max(rect1.x, rect2.x)
            var top = Math.max(rect1.y, rect2.y)
            var right = Math.min(rect1.x + rect1.width, rect2.x + rect2.width)
            var bottom = Math.min(rect1.y + rect1.height, rect2.y + rect2.height)

            var width = Math.max(0, right - left)
            var height = Math.max(0, bottom - top)

            return Qt.rect(left, top, width, height)
        }
    }

    // Timer để kiểm tra lại hover
    Timer {
        id: checkHoverTimer
        interval: 100
        repeat: false
        onTriggered: {
            if (!dragArea.containsMouse && !closeArea.containsMouse && !maximizeArea.containsMouse && !moveMapModeArea.containsMouse) {
                root.isHovered = false
            }
        }
    }

    // Add selection highlight
    Rectangle {
        anchors.fill: parent
        color: "#3B82F640"
        visible: isSelected && !itemDropArea.containsDrag
        z: 1
    }

    // Add selection overlay
    Rectangle {
        anchors.fill: parent
        color: isSelected ? (isDarkTheme ? "#3B82F680" : "#60A5FA80") : "transparent"
        visible: isSelected
    }

    Flow {
        id: controlButtonsRow
        objectName: "controlButtonsRow" // Thêm objectName để có thể tìm kiếm từ PTZ3DControl
        anchors {
            top: parent.top
            right: parent.right
            margins: Math.max(1, Math.min(6, root.width / 60)) // Responsive margins
        }
        width: Math.min(root.width * 0.95, 400) // Limit width to allow wrapping
        layoutDirection: Qt.RightToLeft // Left to right order
        spacing: Math.max(1, Math.min(4, root.width / 100)) // Responsive spacing
        visible: root.isHovered || root.isSelected || root.isMapMoveMode // Show controls when hovered, selected, or in map move mode
        z: 2

        // Calculate button size based on grid item size, with larger minimum
        property real buttonSize: Math.max(22, Math.min(32, root.width / 16))
        property real iconSize: buttonSize * 0.6

        // Close button
        Rectangle {
            width: controlButtonsRow.buttonSize
            height: controlButtonsRow.buttonSize
            radius: width / 7
            color: closeArea.containsMouse ? "rgba(0, 0, 0, 0.2)" : "rgba(0, 0, 0, 0.7)"
            border.color: "#FFFFFF"
            border.width: closeArea.containsMouse ? 1 : 0

            Image {
                anchors.centerIn: parent
                source: isDarkTheme ? "qrc:src/assets/camera_stream/icon_close.svg" : "qrc:src/assets/camera_stream/icon_close_light.svg"
                width: controlButtonsRow.iconSize
                height: controlButtonsRow.iconSize
                fillMode: Image.PreserveAspectFit
            }

            MouseArea {
                id: closeArea
                anchors.fill: parent
                hoverEnabled: true
                onClicked: {
                    // If in maximized mode and this is the maximized item, restore grid first
                    if (gridModel && gridModel.isMaximized && position === gridModel.activeItemPosition) {
                        // console.log("[DEBUG_REMOVE_STREAM] Restoring grid before removing video");
                        gridModel.restoreGrid()
                    }

                    // Then remove the video
                    // console.log("[DEBUG_REMOVE_STREAM] Calling removeVideo for position: " + position);
                    if (gridModel) {
                        var result = gridModel.removeMapItem(position);
                        if (result) console.log("[DEBUG_REMOVE_STREAM] removeVideo result: " + result);
                    }
                }
            }
        }

        // Maximize button
        Rectangle {
            width: controlButtonsRow.buttonSize
            height: controlButtonsRow.buttonSize
            radius: width / 7
            color: maximizeArea.containsMouse ? "rgba(0, 0, 0, 0.2)" : "rgba(0, 0, 0, 0.7)"
            border.color: "#FFFFFF"
            border.width: maximizeArea.containsMouse ? 1 : 0
            visible: root.itemData && root.isHovered && gridModel && (gridModel.getActivePositions().length > 1 || gridModel.isMaximized)

            Image {
                anchors.centerIn: parent
                source: {
                    if (gridModel && gridModel.isMaximized && position === gridModel.activeItemPosition) {
                        return isDarkTheme ? "qrc:src/assets/camera_stream/shrink_camera.svg" : "qrc:src/assets/camera_stream/shrink_camera_light.svg"
                    } else {
                        return isDarkTheme ? "qrc:src/assets/camera_stream/expand_camera.svg" : "qrc:src/assets/camera_stream/expand_camera_light.svg"
                    }
                }
                width: controlButtonsRow.iconSize
                height: controlButtonsRow.iconSize
                fillMode: Image.PreserveAspectFit
            }

            MouseArea {
                id: maximizeArea
                anchors.fill: parent
                hoverEnabled: true
                onClicked: {
                    // console.log("Maximize button clicked - position:", position, "isMaximized:", gridModel.isMaximized, "activeItemPosition:", gridModel.activeItemPosition);

                    // Prevent click during animation
                    if (isMaximizing || isResizing) {
                        // console.log("Ignoring maximize button click - animation in progress");
                        return;
                    }

                    // Simple toggle logic - if maximized, restore; if not maximized, maximize
                    if (gridModel.isMaximized) {
                        // Only restore if this is the maximized item
                        if (position === gridModel.activeItemPosition) {
                            // console.log("Restoring grid from maximize button");
                            isResizing = true;
                            // Reset z-index using calculateZ function
                            root.z = calculateZ();
                            restoreAnimation.start();
                            gridModel.restoreGrid();
                        } else {
                            // console.log("Not the active item, no action taken");
                        }
                    } else {
                        // Maximize this item
                        // console.log("Maximizing grid from button click");
                        isMaximizing = true;
                        // Force z-index update
                        root.z = 4;
                        maximizeAnimation.start();
                        gridModel.maximizeGrid(position);
                    }
                }
            }
        }

        // Focus map button
        Rectangle {
            width: controlButtonsRow.buttonSize
            height: controlButtonsRow.buttonSize
            radius: width / 7
            color: {
                if (root.isMapMoveMode) {
                    return "#4fd1c5" // Active color - using the hover color for consistency
                } else if (moveMapModeArea.containsMouse) {
                    return "rgba(0, 0, 0, 0.2)" // Hover color
                } else {
                    return "rgba(0, 0, 0, 0.7)" // Default color
                }
            }
            border.color: "#FFFFFF"
            border.width: moveMapModeArea.containsMouse || root.isMapMoveMode ? 1 : 0
            visible: true // Always show the map move button for Map2D

            Image {
                anchors.centerIn: parent
                source: isDarkTheme ? "qrc:src/assets/camera_stream/icon_ptz_arrow_off.svg" : "qrc:src/assets/camera_stream/icon_ptz_arrow_off.svg"
                width: controlButtonsRow.iconSize
                height: controlButtonsRow.iconSize
                fillMode: Image.PreserveAspectFit
                opacity: root.isMapMoveMode ? 1.0 : 0.7 // Make it more visible when active
            }

            MouseArea {
                id: moveMapModeArea
                anchors.fill: parent
                hoverEnabled: true
                onClicked: {
                    // Toggle map move mode
                    root.isMapMoveMode = !root.isMapMoveMode
                    console.log("Map move mode:", root.isMapMoveMode)
                }
            }
        }
    }

    // Map2DConverter{
    //     id: _map2DController
    // }

    MapState{
        id: _mapState
        editMode: false
    }

    // Handle itemData changes
    // onItemDataChanged: {
    //     console.log("GridItemMap2D - itemData changed:", itemData)
    //     if (itemData) {
    //         var model = _map2DController.initModel(itemData)
    //         console.log("GridItemMap2D - got model from controller:", model)
    //         map2DonGrid.floorModelFromQML = model
    //     }
    // }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 8
        spacing: 4

        // Video display area
        Rectangle {
            id: contentArea
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "black"

            // Placeholder when no video
            Rectangle {
                anchors.fill: parent
                color: isDarkTheme ? "#2d2d2d" : "#e5e7eb"
                radius: 4
                visible: !root.itemData
            }

            MapState {
                id:idMapState
            }
            Map2DonGrid {
                id: map2DonGrid
                objectName: "map2DonGrid"
                qmlType: true
                floorModelFromQML: (function(){
                    console.log("floorModelFromQML ",root.itemData)
                    return root.itemData
                })()
                mapStateFromQML: idMapState
                width: parent.width
                height: parent.height

                // Add property to track map move mode
                property bool isMapMoveEnabled: root.isMapMoveMode

                // Connect to map move mode changes
                Connections {
                    target: root
                    function onIsMapMoveModeChanged() {
                        map2DonGrid.isMapMoveEnabled = root.isMapMoveMode
                        console.log("Map2DonGrid: Map move mode changed to:", root.isMapMoveMode)
                    }
                }
            }
        }
    }

    // animation
    scale: 0.5  // Bắt đầu từ kích thước nhỏ
    opacity: 0  // Ẩn ban đầu
    Behavior on scale { NumberAnimation { duration: 300; easing.type: Easing.OutBack } }

    Component.onCompleted: {
        scale = 1
        opacity = 1
    }

    Connections {
        target: gridModel
        function onLayoutChanged(activePositions) {
            // Sử dụng activePositions được truyền từ signal thay vì gọi getActivePositions
            // Không cần làm gì ở đây vì StreamingScreen1.qml đã xử lý layoutChanged
        }
    }

    // Add maximize/restore animations
    NumberAnimation {
        id: maximizeAnimation
        target: root
        property: "scale"
        from: 1.0
        to: 1.0
        duration: 300
        easing.type: Easing.OutQuad
        onStarted: {
            isMaximizing = true
            // Đặt z-index cao khi bắt đầu animation
            root.z = 5000
        }
        onStopped: {
            isMaximizing = false
            // Đặt lại z-index về giá trị phù hợp cho chế độ fullscreen
            root.z = calculateZ()
            console.log("Maximize animation completed, z-index set to:", root.z)
        }
    }


    NumberAnimation {
        id: restoreAnimation
        target: root
        property: "scale"
        from: 1.0
        to: 1.0
        duration: 300
        easing.type: Easing.OutQuad
        onStarted: {
            isResizing = true
            // Đặt z-index cao khi bắt đầu animation
            root.z = 5000
            // No zoom reset needed for map
        }
        onStopped: {
            isResizing = false
            // Đặt lại z-index về giá trị thấp hơn khi kết thúc
            root.z = calculateZ()
            console.log("Restore animation completed, z-index reset to:", root.z)
        }
    }



    // Hàm kiểm tra xem có thể resize đến kích thước mới không
    function canResizeToSize(newWidthCells, newHeightCells) {
        if (!gridModel) return false;

        // Vị trí bắt đầu của map hiện tại
        var startRow = Math.floor(position / gridModel.columns);
        var startCol = position % gridModel.columns;

        // Kiểm tra xem kích thước mới có vượt quá lưới không
        if (startRow + newHeightCells > gridModel.rows || startCol + newWidthCells > gridModel.columns) {
            // console.log("Resize would exceed grid boundaries");
            return false;
        }

        // Kiểm tra từng ô trong vùng resize mới
        for (var row = startRow; row < startRow + newHeightCells; row++) {
            for (var col = startCol; col < startCol + newWidthCells; col++) {
                var checkPos = row * gridModel.columns + col;

                // Bỏ qua vị trí hiện tại và các ô đã thuộc về map này
                if (checkPos === position) continue;
                if (root.parent.activeItems[checkPos] === position) continue;

                // Kiểm tra xem ô có bị chiếm bởi item khác không
                if (checkPos in root.parent.activeItems) {
                    var occupyingItem = root.parent.activeItems[checkPos];
                    if (occupyingItem !== undefined && occupyingItem !== position) {
                        // console.log("Position " + checkPos + " is occupied by another item");
                        return false;
                    }
                }

                // Kiểm tra xem ô có thuộc về phần mở rộng của item khác không
                for (var pos in root.parent.activeItems) {
                    if (pos === position.toString()) continue;

                    var item = root.parent.activeItems[pos];
                    if (!item) continue;

                    var itemDimensions = gridModel ? gridModel.getCellDimensions(parseInt(pos)) : {"width": 1, "height": 1};
                    var itemStartCol = gridModel ? Math.floor(parseInt(pos) % gridModel.columns) : 0;
                    var itemStartRow = gridModel ? Math.floor(parseInt(pos) / gridModel.columns) : 0;
                    var itemEndCol = itemStartCol + itemDimensions.width - 1;
                    var itemEndRow = itemStartRow + itemDimensions.height - 1;

                    if (col >= itemStartCol && col <= itemEndCol &&
                        row >= itemStartRow && row <= itemEndRow) {
                        // console.log("Position conflicts with extended item at " + pos);
                        return false;
                    }
                }
            }
        }

        return true;
    }

    // Hàm kiểm tra có item trong ô bên phải - giữ lại để tương thích
    function isItemInRight() {
        if (!gridModel) return false;
        var startRow = Math.floor(position / gridModel.columns);
        var startCol = position % gridModel.columns;
        var currentDimensions = gridModel.getCellDimensions(position);
        var widthCells = currentDimensions.width;

        return !canResizeToSize(widthCells + 1, currentDimensions.height);
    }

    // Hàm kiểm tra có item trong ô bên dưới - giữ lại để tương thích
    function isItemInBottom() {
        if (!gridModel) return false;
        var startRow = Math.floor(position / gridModel.columns);
        var startCol = position % gridModel.columns;
        var currentDimensions = gridModel.getCellDimensions(position);
        var heightCells = currentDimensions.height;

        return !canResizeToSize(currentDimensions.width, heightCells + 1);
    }

    // Hàm kiểm tra có item trong ô bên trái - giữ lại để tương thích
    function isItemInLeft() {
        if (!gridModel) return false;
        var leftPos = position - 1; // Giả sử ô bên trái là position - 1
        return leftPos >= 0 &&
               root.parent && root.parent.activeItems && root.parent.activeItems[leftPos] !== undefined;
    }

    // Hàm kiểm tra có item trong ô bên trên - giữ lại để tương thích
    function isItemInTop() {
        if (!gridModel) return false;
        var topPos = position - gridModel.columns; // Giả sử ô bên trên là position - số cột
        return topPos >= 0 &&
               root.parent && root.parent.activeItems && root.parent.activeItems[topPos] !== undefined;
    }
}