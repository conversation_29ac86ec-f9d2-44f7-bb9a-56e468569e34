import copy
import logging
import traceback
import time
import json
import ast
from queue import Queue
from PySide6.QtCore import QObject, Signal, Slot, Property, QAbstractListModel, Qt, QModelIndex, QThread, QTimer, QPoint, QCoreApplication
from src.common.model.device_models import TabType
from src.common.model.tab_model import TabModel, ItemType, SignalType, Tab
from src.common.model.camera_model import camera_model_manager, CameraModel
from src.common.model.group_model import group_model_manager
from src.common.model.event_data_model import event_manager
from src.common.model.aiflows_model import aiflow_model_manager
from src.common.controller.main_controller import main_controller
from src.common.qml.models.grid_item_selected import grid_item_selected
from src.common.onvif_api.worker_thread import WorkerThread
from pyjoystick.sdl2 import Key
from src.common.onvif_api.calib_data import CalibData,Manufacturer,Avigilon
from src.common.camera.video_capture import video_capture_controller
from src.common.widget.dialogs.remove_camera_dialog import RemoveCameraDialog
from src.common.qml.models.common_enum import CommonEnum
from src.common.qml.models.map_controller import MapModel,BuildingModel,FloorModel,floor_manager,map_manager
from src.common.model.main_tree_view_model import TreeType
from src.common.threads.sub_thread import SubThread
from src.styles.style import Style
logger = logging.getLogger(__name__)

class GridModel(QAbstractListModel):
    gridChanged = Signal(int, int)  # columns, rows
    layoutChanged = Signal(list)  # List of active positions in current layout
    columnsChanged = Signal(int)  # Signal for columns property
    rowsChanged = Signal(int)    # Signal for rows property
    videoInfoChanged = Signal(int, QObject, bool, bool)  # Signal for video info changes: position, camera_id, isPlaying, supportsPTZ
    mapInfoChanged = Signal(int, QObject)  # Signal for map info changes: position, map_data
    isMaximizedChanged = Signal(bool)  # Signal for maximized state changes
    activeItemPositionChanged = Signal(int)  # Signal for active item po sition changes
    videoPositionChanged = Signal(int, int)  # old_pos, new_pos
    # Define roles for the model
    PositionRole = Qt.UserRole + 1
    UrlRole = Qt.UserRole + 2
    NameRole = Qt.UserRole + 3
    IsPlayingRole = Qt.UserRole + 4
    cellDimensionsChanged = Signal(int, int, int)  # position, width, height
    isDarkThemeChanged = Signal()
    backgroundColorChanged = Signal()
    foregroundColorChanged = Signal()
    borderColorChanged = Signal()
    headerColorChanged = Signal()
    itemBackgroundColorChanged = Signal()
    hoverColorChanged = Signal()
    dragActiveColorChanged = Signal()
    videoBackgroundColorChanged = Signal()
    aiSolutionsChanged = Signal()
    aiSolutionsError = Signal(str)
    # Thêm signal mới để thông báo khi tất cả các item đã sẵn sàng
    allItemsReady = Signal()
    # Signal cho số lượng camera thay đổi
    cameraCountChanged = Signal(int)
    # Signal cho animation restore từ fullscreen
    restoreAnimationStarted = Signal(int)  # position của camera đang ở chế độ fullscreen
    isSaveChanged = Signal()
    saveSignalChanged = Signal(QObject, bool)
    removeAllItemChanged = Signal()
    availableTabsChanged = Signal()  # Signal for available tabs property

    # New action signals for centralized control
    # These signals will be emitted when actions are requested from QML
    addCameraRequested = Signal(int, str)  # position, camera_data
    addGroupRequested = Signal(int, str)  # position, group_data
    addGroupToPositionsRequested = Signal(str)  # group_info_json
    removeItemsRequested = Signal('QVariantList')  # list of positions
    swapItemsRequested = Signal(int, int)  # source_pos, target_pos
    toggleFullscreenRequested = Signal(int)  # position
    loadGridRequested = Signal(str)  # grid_id
    clearGridRequested = Signal()  # no parameters

    # Result signals to notify QML after processing
    actionCompleted = Signal(str, bool, str)  # action_type, success, message
    gridStateChanged = Signal('QVariant')  # complete grid state

    def __init__(self, parent=None, callback = None, data: dict = {}, controller = None):
        super().__init__(parent)

        # Connect to tab model manager signals to update available tabs
        try:
            from src.common.model.tab_model import tab_model_manager
            tab_model_manager.add_tab_model_signal.connect(self._onTabAdded)
            tab_model_manager.add_tab_model_list_signal.connect(self._onTabListAdded)
        except Exception as e:
            logger.error(f"Error connecting to tab model manager signals: {e}")
        self._active_cells = {}  # Dictionary mapping grid positions to camera models (previously just camera IDs)
        self._cell_dimensions = {}  # {position: {"width": int, "height": int}} - Store cell dimensions
        self._previous_columns = 1  # Số cột trước khi phóng to
        self._previous_rows = 1  # Số hàng trước khi phóng to
        self.originGridModel = None
        # Đặt isSave = True khi khởi tạo
        self._isSave = True

        self._columns = self._previous_columns
        self._rows = self._previous_rows
        self._active_item_position = None
        self._next_position = 0
        self._layout = []  # Current grid layout
        self._is_maximized = False
        self._video_urls = {}  # Track video URLs by position
        self._auto_resize_enabled = True  # Flag to control automatic grid resizing
        self._data = data
        self._currentData = copy.deepcopy(data)
        self.controller = self.get_controller()
        self._is_dark_theme = True
        self._background_color = "#1a1a1a"
        self._foreground_color = "#ffffff"
        self._border_color = "#333333"
        self._header_color = "#1f2937"
        self._item_background_color = "#2d2d2d"
        self._hover_color = "#3d3d3d"
        self._drag_active_color = "#4d4d4d"
        self._video_background_color = "#1f2937"
        self._ai_solutions = []
        self._ai_solutions_loaded = False
        self._ai_solutions_loading = False
        self._ai_solutions_error = ""
        # Thêm biến để theo dõi trạng thái của các item
        self._ready_items = {}      # Dictionary để lưu trữ các item đã sẵn sàng
        # Biến đếm số lượng camera
        self._camera_count = 0      # Số lượng camera hiện tại trong grid
        self._grid_item_selected = grid_item_selected
        self.calib_data = self.get_calib_data()
        self.coordinate_queue = Queue()
        self.process_queue_thread = WorkerThread(parent=self, target=self.process_queue, callback=self.on_process_queue_complete)
        self.callback = callback
        self._ptz_speed = 0.5  # Default PTZ speed (0.0 to 1.0)
        self._current_zoom_camera = None  # Thông tin camera đang zoom
        self._zoom_timer = None  # Timer để dừng zoom

        # Connect action signals to handlers
        self._connectActionSignals()

    def _connectActionSignals(self):
        """Connect action request signals to their handlers"""
        self.addCameraRequested.connect(self._handleAddCameraRequest)
        self.addGroupRequested.connect(self._handleAddGroupRequest)
        self.addGroupToPositionsRequested.connect(self._handleAddGroupToPositionsRequest)
        self.removeItemsRequested.connect(self._handleRemoveItemsRequest)
        self.swapItemsRequested.connect(self._handleSwapItemsRequest)
        self.toggleFullscreenRequested.connect(self._handleToggleFullscreenRequest)
        self.loadGridRequested.connect(self._handleLoadGridRequest)
        self.clearGridRequested.connect(self._handleClearGridRequest)

    def get_controller(self):
        from src.common.controller.controller_manager import controller_manager
        return controller_manager.get_controller(server_ip=self.get_property("server_ip"))

    def setOriginGridModel(self, gridModel):
        self.originGridModel = gridModel
        self.originGridModel.addCameraRequested.connect(self._handleAddCameraRequest)
        self.originGridModel.addGroupRequested.connect(self._handleAddGroupRequest)
        self.originGridModel.addGroupToPositionsRequested.connect(self._handleAddGroupToPositionsRequest)
        self.originGridModel.removeItemsRequested.connect(self._handleRemoveItemsRequest)
        self.originGridModel.swapItemsRequested.connect(self._handleSwapItemsRequest)
        self.originGridModel.toggleFullscreenRequested.connect(self._handleToggleFullscreenRequest)
        self.originGridModel.loadGridRequested.connect(self._handleLoadGridRequest)
        self.originGridModel.clearGridRequested.connect(self._handleClearGridRequest)

    @Slot(int, 'QVariant')
    def _handleAddCameraRequest(self, position, camera_id):
        """Handle add camera request from QML"""
        try:
            logger.debug(f"Handling add camera request at position {position}")
            # Use existing addCamera method
            result = self.addCamera(position, camera_id)
            if result is not None:
                self.actionCompleted.emit("addCamera", True, f"Camera added at position {result}")
            else:
                self.actionCompleted.emit("addCamera", False, "Failed to add camera")
        except Exception as e:
            logger.error(f"Error handling add camera request: {e}")
            self.actionCompleted.emit("addCamera", False, str(e))

    @Slot(int, str)
    def _handleAddGroupRequest(self, position, group_id):
        """Handle add group request from QML"""
        try:
            self.isSave = False
            logger.debug(f"Handling add group request at position {position} - {group_id}")
            # Use existing addGroup method
            result = self.addGroup(position, group_id)
            if result is not None:
                self.actionCompleted.emit("addGroup", True, f"Group added starting at position {result}")
            else:
                self.actionCompleted.emit("addGroup", False, "Failed to add group")
        except Exception as e:
            logger.error(f"Error handling add group request: {e}")
            self.actionCompleted.emit("addGroup", False, str(e))

    @Slot(str)
    def _handleAddGroupToPositionsRequest(self, group_info_json):
        """Handle add group to specific positions request from QML"""
        try:
            self.isSave = False
            logger.info(f"Handling add group to positions request: {group_info_json}")
            # Use existing addGroupToPositions method
            result = self.addGroupToPositions(group_info_json)
            if result is not None:
                self.actionCompleted.emit("addGroup", True, f"Group added to multiple positions")
            else:
                self.actionCompleted.emit("addGroup", False, "Failed to add group to positions")
        except Exception as e:
            logger.error(f"Error handling add group to positions request: {e}")
            self.actionCompleted.emit("addGroup", False, str(e))

    @Slot('QVariantList')
    def _handleRemoveItemsRequest(self, positions):
        """Handle remove items request from QML"""
        try:
            logger.info(f"Handling remove items request for positions: {positions}")
            if len(positions) == 1:
                # Single item removal
                result = self.removeVideo(positions[0])
            else:
                # Multiple items removal
                result = self.removeMultipleVideos(positions)

            if result:
                self.actionCompleted.emit("removeItems", True, f"Removed {len(positions)} items")
            else:
                self.actionCompleted.emit("removeItems", False, "Failed to remove items")
        except Exception as e:
            logger.error(f"Error handling remove items request: {e}")
            self.actionCompleted.emit("removeItems", False, str(e))

    @Slot(int, int)
    def _handleSwapItemsRequest(self, source_pos, target_pos):
        """Handle swap items request from QML"""
        try:
            logger.debug(f"Handling swap request between {source_pos} and {target_pos}")
            # Use existing swapPositions method
            result = self.swapPositions(source_pos, target_pos)
            if result:
                self.actionCompleted.emit("swapItems", True, f"Swapped positions {source_pos} and {target_pos}")
            else:
                self.actionCompleted.emit("swapItems", False, "Failed to swap items")
        except Exception as e:
            logger.error(f"Error handling swap items request: {e}")
            self.actionCompleted.emit("swapItems", False, str(e))

    @Slot(int)
    def _handleToggleFullscreenRequest(self, position):
        """Handle toggle fullscreen request from QML"""
        try:
            print(f"🔍 [GRID MANAGER] Toggle fullscreen request received for position {position}")
            print(f"🔍 [GRID MANAGER] Current state - is_maximized: {self._is_maximized}")
            print(f"🔍 [GRID MANAGER] Current active position: {self._active_item_position}")
            print(f"🔍 [GRID MANAGER] Grid size: {self._columns}x{self._rows}")

            logger.debug(f"Handling toggle fullscreen request for position {position}")
            if self._is_maximized:
                print(f"✅ [GRID MANAGER] Restoring from fullscreen...")
                # Restore from fullscreen
                self.restoreGrid()
                self.actionCompleted.emit("toggleFullscreen", True, "Restored from fullscreen")
                print(f"✅ [GRID MANAGER] Restore completed")
            else:
                print(f"✅ [GRID MANAGER] Maximizing to fullscreen...")
                # Enter fullscreen
                self.maximizeGrid(position)
                self.actionCompleted.emit("toggleFullscreen", True, f"Maximized position {position}")
                print(f"✅ [GRID MANAGER] Maximize completed")
        except Exception as e:
            print(f"❌ [GRID MANAGER] Error handling toggle fullscreen request: {e}")
            logger.error(f"Error handling toggle fullscreen request: {e}")
            self.actionCompleted.emit("toggleFullscreen", False, str(e))

    @Slot(str)
    def _handleLoadGridRequest(self, grid_id):
        """Handle load grid request from QML"""
        try:
            logger.debug(f"Handling load grid request for grid_id: {grid_id}")
            # TODO: Implement grid loading logic
            self.actionCompleted.emit("loadGrid", False, "Not implemented yet")
        except Exception as e:
            logger.error(f"Error handling load grid request: {e}")
            self.actionCompleted.emit("loadGrid", False, str(e))

    @Slot()
    def _handleClearGridRequest(self):
        """Handle clear grid request from QML"""
        try:
            logger.debug("Handling clear grid request")
            # Use existing removeAllItem method
            self.removeAllItem()
            self.actionCompleted.emit("clearGrid", True, "Grid cleared")
        except Exception as e:
            logger.error(f"Error handling clear grid request: {e}")
            self.actionCompleted.emit("clearGrid", False, str(e))

    def get_property(self, key, default=None):
        """Lấy giá trị từ self.data, nếu không có thì trả về default."""
        return self._data.get(key, default)

    def set_property(self, key, value):
        """Cập nhật giá trị trong self.data."""
        self._data[key] = value

    @Property(bool,notify=isSaveChanged)
    def isSave(self):
        return self._isSave

    @isSave.setter
    def isSave(self, value: bool):
        if self._isSave != value:
            self._isSave = value
            self.isSaveChanged.emit()
            self.saveSignalChanged.emit(self, value)

    def loadData(self):
        # Nếu là SavedView, lấy thông số columns và rows từ currentGrid
        if self.get_property("type") != TabType.Invalid:
            try:
                # Lấy thông tin currentGrid
                grid_config_str = self.get_property("currentGrid")
                logger.debug(f"Grid configuration: {grid_config_str}")

                # Phân tích chuỗi JSON
                if isinstance(grid_config_str, str):
                    grid_config = json.loads(grid_config_str.replace("'", '"'))
                    if 'row' in grid_config and 'col' in grid_config:
                        self._previous_columns = int(grid_config['col'])
                        self._previous_rows = int(grid_config['row'])
                        logger.debug(f"Set grid size from saved view: {self._previous_columns}x{self._previous_rows}")
            except Exception as e:
                logger.error(f"Error setting grid size from saved view: {e}")
                logger.error(traceback.format_exc())
        if self.get_property("type") != TabType.Invalid:
            # Đặt isSave = True khi mở một saved view
            self.isSave = True
            logger.debug(f"Tab model is a saved view, loading cameras... Set isSave = True")
            QTimer.singleShot(100, self.load_saved_view)

    def on_process_queue_complete(self, result=None):
        """Callback for process_queue_thread completion"""
        if result is not None:
            logger.debug(f"Process queue completed with result: {result}")
        else:
            logger.debug("Process queue completed")

    def get_calib_data(self):
        # Mặc định sử dụng dữ liệu hiệu chuẩn không hợp lệ
        return CalibData.get_data(type=Manufacturer.Invalid)

    def process_queue(self):
        logger.debug(f'init process_queue')
        # Khởi tạo biến thời gian
        self.previous_time = time.time()

        while True:
            msg = self.coordinate_queue.get()
            if msg is None:
                # Dừng xử lý khi nhận được tín hiệu None
                logger.debug("Received stop signal in queue")
                break

            self.current_time = time.time()
            if (self.current_time - self.previous_time) > 0.2:
                logger.debug(f'process_queue = {msg} -- {self.current_time - self.previous_time}s')
                self.previous_time = time.time()

                # Xử lý tọa độ PTZ nếu có camera_id và controller
                if hasattr(msg, 'camera_id') and hasattr(msg, 'controller'):
                    msg['controller'].ptz_continuous_move(
                        parent=self,
                        cameraId=msg['camera_id'],
                        x=msg['pan'],
                        y=msg['tilt'],
                        zoom=msg['zoom']
                    )

            self.coordinate_queue.task_done()


    def rowCount(self, parent=QModelIndex()):
        return len(self._active_cells)

    def _calculateGridSize(self, count):
        """Calculate optimal grid size - always returns square grid"""
        if count <= 1: return (1, 1)
        elif count == 2: return (2, 2)  # Changed from (2, 1) to (2, 2) for square grid
        elif count <= 4: return (2, 2)
        elif count <= 6: return (3, 3)  # Changed from (3, 2) to (3, 3) for square grid
        elif count <= 9: return (3, 3)
        elif count <= 12: return (4, 4)  # Changed from (4, 3) to (4, 4) for square grid
        elif count <= 16: return (4, 4)
        elif count <= 25: return (5, 5)
        elif count <= 36: return (6, 6)
        elif count <= 49: return (7, 7)
        elif count <= 64: return (8, 8)
        elif count <= 81: return (9, 9)
        elif count <= 100: return (10, 10)
        elif count <= 121: return (11, 11)
        else:
            # Maximum 12x12 = 144 cameras
            return (12, 12)



    @Slot(int, str)
    def addCamera(self, position, camera_id):
        """Thêm một camera vào grid"""

        # Find all available positions in order
        max_pos = max(self._active_cells.keys()) if self._active_cells else -1
        all_positions = set(range(max_pos + 2))  # +2 to have one more position after max
        available_positions = sorted(list(all_positions - set(self._active_cells.keys())))

        if not available_positions:
            logger.warning("No available positions to add camera")
            return None

        # Nếu không có vị trí được chỉ định, sử dụng vị trí đầu tiên có sẵn
        if position is None:
            position = available_positions[0]
        # Nếu vị trí đã bị chiếm
        elif position in self._active_cells:
            # Nếu auto_resize_enabled là True, sử dụng vị trí trống tiếp theo
            if self._auto_resize_enabled:
                position = available_positions[0]
            # Nếu auto_resize_enabled là False, vẫn sử dụng vị trí đó (thay thế camera cũ)
        elif position in self._active_cells.values():
            # Nếu auto_resize_enabled là True, sử dụng vị trí trống tiếp theo
            if self._auto_resize_enabled:
                position = available_positions[0]
            # Nếu auto_resize_enabled là False, vẫn sử dụng vị trí đó (thay thế camera cũ)

        if not camera_id:
            logger.warning(f"[DEBUG_ADD_CAMERA] No camera ID found in camera_data: {camera_id}")
            return None

        # Get camera model
        camera_model = camera_model_manager.get_camera_model(id=camera_id)
        if not camera_model:
            logger.error(f"Camera model not found for id: {camera_id}")
            return None

        try:
            # Kiểm tra xem grid có đang ở chế độ fullscreen không
            was_fullscreen = self._is_maximized

            # Nếu đang ở chế độ fullscreen, tắt chế độ fullscreen trước khi thêm camera mới
            if was_fullscreen:
                logger.debug(f"🔄 [DEBUG_ADD_CAMERA] Grid is in fullscreen mode, restoring to normal size before adding camera")
                # Sử dụng phương thức restoreGrid có sẵn để đảm bảo tính nhất quán
                self.restoreGrid()
                logger.debug(f"🔄 [DEBUG_ADD_CAMERA] Grid restored to normal size: {self._columns}x{self._rows}")

            # Begin insert row
            self.beginInsertRows(QModelIndex(), len(self._active_cells), len(self._active_cells))

            # Add camera model to active cells instead of just the ID
            self._active_cells[position] = camera_model

            # Luôn cập nhật _cell_dimensions, ngay cả khi kích thước là 1x1
            self._cell_dimensions[position] = {
                "width": 1,
                "height": 1
            }
            logger.debug(f"🔄 [DEBUG_ADD_CAMERA] Set cell dimensions for position {position}: 1x1")

            # Update grid size if auto-resize is enabled
            if self._auto_resize_enabled:
                new_count = max(self._active_cells.keys(), default=0) + 1
                new_cols, new_rows = self._calculateGridSize(new_count)

                grid_changed = False
                if new_cols != self._columns:
                    self._columns = new_cols
                    self.columnsChanged.emit(self._columns)
                    grid_changed = True
                if new_rows != self._rows:
                    self._rows = new_rows
                    self.rowsChanged.emit(self._rows)
                    grid_changed = True

                # If grid size changed, emit cellDimensionsChanged for all existing cameras
                # so they can update their visual size based on new grid dimensions
                if grid_changed:
                    for pos, dimensions in self._cell_dimensions.items():
                        if pos in self._active_cells:  # Only for active cameras
                            self.cellDimensionsChanged.emit(pos, dimensions["width"], dimensions["height"])

            # Update next_position
            max_pos = max(self._active_cells.keys())
            all_positions = set(range(max_pos + 2))
            available_positions = sorted(list(all_positions - set(self._active_cells.keys())))
            self._next_position = available_positions[0] if available_positions else max_pos + 1

            # End insert row operation
            self.endInsertRows()

            # Check if the camera supports PTZ
            supports_ptz = False
            ptz_caps = camera_model.get_property("ptzCap", [])
            supports_ptz = ptz_caps is not None and len(ptz_caps) > 0

            # Emit video info changed signal with PTZ support information
            self.videoInfoChanged.emit(position, camera_model, True, supports_ptz)

            # Không gọi start_processing() ở đây nữa
            # Thay vào đó, chúng ta sẽ đợi itemReady được gọi

            logger.debug(f"✅ Successfully added camera at position {position}")
            return position

        except Exception as e:
            logger.error(f"❌ Error during camera addition: {e}")
            # Ensure we end the insert row operation if an error occurred
            try:
                self.endInsertRows()
            except:
                pass
            return None

    @Slot(int, result=bool)
    def supportsPTZ(self, position: int):
        """Check if the camera at the specified position supports PTZ

        Args:
            position: The position of the camera in the grid

        Returns:
            bool: True if the camera supports PTZ, False otherwise
        """
        try:
            # Validate position
            if position is None or not isinstance(position, int) or position < 0:
                logger.warning(f"Invalid position: {position}")
                return False

            # Check if the position is valid and has a camera
            if position not in self._active_cells:
                logger.warning(f"No camera found at position {position}")
                return False

            # Get the camera ID
            camera_id = self._active_cells[position]
            if not camera_id:
                logger.warning(f"No camera ID found for position {position}")
                return False

            # Get the camera model
            camera_model = camera_model_manager.get_camera_model(id=camera_id)
            if not camera_model:
                logger.warning(f"Camera model not found for ID: {camera_id}")
                return False

            # Check if the camera supports PTZ
            if not hasattr(camera_model, 'data') or not camera_model.data:
                logger.warning(f"Camera model has no data attribute: {camera_id}")
                return False

            # Check if the camera has PTZ capabilities
            ptz_caps = camera_model.get_property("ptzCap", [])
            supports_ptz = ptz_caps is not None and len(ptz_caps) > 0

            logger.debug(f"Camera at position {position} supports PTZ: {supports_ptz}")
            return supports_ptz

        except Exception as e:
            logger.error(f"Error checking PTZ support: {e}")
            logger.error(traceback.format_exc())
            return False

    @Slot(int)
    def removeVideo(self, position: int):
        """Remove a video and update grid if needed"""
        # Validate position
        if position is None or not isinstance(position, int) or position < 0:
            logger.debug(f"[DEBUG_REMOVE_STREAM] ❌ Invalid position: {position}")
            return False

        logger.debug(f"[DEBUG_REMOVE_STREAM] 🔄 Starting video removal process for position: {position}")
        logger.debug(f"[DEBUG_REMOVE_STREAM] Current active cells: {self._active_cells}")
        logger.debug(f"[DEBUG_REMOVE_STREAM] Call stack: {traceback.format_stack()}")

        # Find the key containing the target position value
        target_key = None
        for key, value in self._active_cells.items():
            if key == position:  # Changed from value == position to key == position
                target_key = key
                break

        # If we found the key with our target position value
        if target_key is not None:
            logger.debug(f"[DEBUG_REMOVE_STREAM] ✅ Found video at position {target_key}")

            # Get camera information for confirmation dialog
            camera_model = self._active_cells[target_key]
            camera_name = ""
            camera_id = None

            if camera_model and hasattr(camera_model, 'data') and camera_model.data:
                camera_name = camera_model.get_property('name')
                camera_id = camera_model.get_property('id')

            # Import RemoveCameraDialog

            # Kiểm tra xem có nên hiển thị dialog hay không
            should_show_dialog = RemoveCameraDialog.shouldShow()
            proceed_with_deletion = True

            if should_show_dialog:
                # Hiển thị dialog xác nhận
                dialog_title = "Delete Camera"
                dialog_description = f"Are you sure you want to delete camera '{camera_name}'?"
                if not camera_name:
                    dialog_description = "Are you sure you want to delete this camera?"

                dialog = RemoveCameraDialog(
                    title=dialog_title,
                    description=dialog_description
                )

                # Nếu người dùng xác nhận xóa
                proceed_with_deletion = dialog.exec()

            # Nếu người dùng xác nhận xóa hoặc đã chọn "Không hiển thị lại"
            if proceed_with_deletion:
                try:
                    # Handle video stream removal based on camera usage count across all tabs
                    if camera_id:
                        # Count how many instances of this camera are currently in the current grid
                        current_grid_usage_count = sum(1 for _, cam_model in self._active_cells.items()
                                                     if cam_model and hasattr(cam_model, 'get_property')
                                                     and cam_model.get_property('id') == camera_id)

                        # Check camera usage across all tabs
                        total_usage_count = current_grid_usage_count
                        try:
                            # Import để tránh circular import
                            from src.common.controller.main_controller import main_controller

                            # Lấy camera usage từ tất cả tab khác
                            camera_screen = main_controller.list_parent.get('CameraScreen')

                            if camera_screen and hasattr(camera_screen, 'new_custom_tab_widget'):
                                tab_widget = camera_screen.new_custom_tab_widget

                                # Duyệt qua tất cả tab khác
                                for i in range(tab_widget.tab_widget.tab_bar.count()):
                                    widget = camera_screen.center_stacked_widget.widget(i)
                                    if hasattr(widget, 'gridModel') and widget.gridModel != self:  # Skip current tab
                                        gridModel = widget.gridModel
                                        other_tab_usage = sum(1 for _, cam_model in gridModel._active_cells.items()
                                                            if cam_model and hasattr(cam_model, 'get_property')
                                                            and cam_model.get_property('id') == camera_id)
                                        total_usage_count += other_tab_usage

                            logger.debug(f"Camera {camera_id} usage: current_grid={current_grid_usage_count}, total_across_tabs={total_usage_count}")

                        except Exception as e:
                            logger.error(f"Error checking camera usage across tabs: {e}")
                            # Nếu có lỗi, chỉ dùng current grid usage để tránh unregister nhầm
                            total_usage_count = current_grid_usage_count

                        try:
                            if camera_model and camera_id in video_capture_controller._list_video_capture:
                                # If this will be the last instance after removal, unregister completely
                                if total_usage_count <= 1:
                                    logger.debug(f"Last instance across all tabs - unregistering camera: {camera_id}")
                                    # Get all stream types for this camera
                                    for stream_type, video_capture in list(video_capture_controller._list_video_capture[camera_id].items()):
                                        try:
                                            video_capture.stop_capture()
                                        except Exception as e:
                                            logger.error(f"Error stopping capture for camera {camera_id}, stream type {stream_type}: {e}")
                                else:
                                    # If there are other instances in other tabs, just remove share_frame_signal connection
                                    logger.debug(f"Other instances exist in other tabs - removing share_frame_signal for camera: {camera_id}")
                                    # Note: The actual share_frame_signal disconnection will be handled by CustomVideo.qml
                                    # when it receives the videoInfoChanged signal with None camera_model
                        except Exception as e:
                            logger.error(f"Error handling video capture for camera {camera_id}: {e}")

                    # Begin remove row
                    index = list(self._active_cells.keys()).index(target_key)
                    logger.debug(f"[DEBUG_REMOVE_STREAM] Removing row at index: {index}")
                    self.beginRemoveRows(QModelIndex(), index, index)

                    # Remove video from active cells
                    if target_key in self._active_cells:
                        # Get camera model and dimensions before removing
                        camera_model = self._active_cells[target_key]
                        cell_dims = self._cell_dimensions.get(target_key, {"width": 1, "height": 1})

                        # Remove main position
                        logger.debug(f"[DEBUG_REMOVE_STREAM] 🗑️ Removing camera ID {self._active_cells[target_key]} from active cells")
                        del self._active_cells[target_key]

                        # Remove all related positions (for resized cameras)
                        start_row = target_key // self._columns
                        start_col = target_key % self._columns

                        # Remove all occupied positions
                        for row in range(start_row, start_row + cell_dims["height"]):
                            for col in range(start_col, start_col + cell_dims["width"]):
                                occupied_pos = row * self._columns + col
                                if occupied_pos in self._active_cells and self._active_cells[occupied_pos] == camera_model:
                                    logger.debug(f"[DEBUG_REMOVE_STREAM] 🗑️ Removing related position {occupied_pos}")
                                    del self._active_cells[occupied_pos]

                    # Remove cell dimensions
                    if target_key in self._cell_dimensions:
                        logger.debug(f"[DEBUG_REMOVE_STREAM] 🗑️ Removing cell dimensions for position {target_key}")
                        del self._cell_dimensions[target_key]

                    # Remove video info if it exists
                    if hasattr(self, '_video_info') and target_key in self._video_info:
                        del self._video_info[target_key]

                    # End remove row
                    self.endRemoveRows()

                    # Update next_position
                    self._next_position = self._calculate_next_position()

                    logger.debug(f"[DEBUG_REMOVE_STREAM] ✅ Successfully removed video at position {target_key}")
                    logger.debug(f"[DEBUG_REMOVE_STREAM] Updated active positions: {sorted(self._active_cells.keys())}")
                    logger.debug(f"[DEBUG_REMOVE_STREAM] Updated next available position: {self._next_position}")

                    # Cập nhật kích thước grid dựa trên số lượng camera thực sự
                    if self._auto_resize_enabled:
                        logger.debug(f"[DEBUG_REMOVE_STREAM] 🔄 Updating grid size after camera removal")
                        # Đếm số lượng camera thực sự (không phải None)
                        real_count = sum(1 for v in self._active_cells.values() if v is not None)
                        # Cập nhật _active_cells để chỉ giữ lại các ô có camera thực sự
                        self._active_cells = {k: v for k, v in self._active_cells.items() if v is not None}
                        self._updateGrid()

                    # Emit signal to remove UI with empty values
                    logger.debug(f"[DEBUG_REMOVE_STREAM] 📣 Emitting videoInfoChanged signal for position {target_key} with empty camera_id")
                    self.videoInfoChanged.emit(target_key, None, False, False)

                    return True
                except Exception as inner_e:
                    logger.debug(f"[DEBUG_REMOVE_STREAM] ❌ Error during video removal operations: {inner_e}")
                    logger.debug(f"[DEBUG_REMOVE_STREAM] Traceback: {traceback.format_exc()}")
                    # Ensure we end the remove rows operation even if an error occurred
                    self.endRemoveRows()
                    return False
            else:
                # User canceled deletion
                logger.debug(f"User canceled deletion of camera at position {target_key}")
                return False
        else:
            logger.debug(f"[DEBUG_REMOVE_STREAM] ❌ No video found at position {position}")
            return False

    @Slot(int, result=bool)
    def removeMapItem(self, position: int):
        """Remove a map item and update grid if needed"""
        # Validate position
        if position is None or not isinstance(position, int) or position < 0:
            logger.debug(f"[DEBUG_REMOVE_MAP] ❌ Invalid position: {position}")
            return False

        logger.debug(f"[DEBUG_REMOVE_MAP] 🔄 Starting map removal process for position: {position}")
        logger.debug(f"[DEBUG_REMOVE_MAP] Current active cells: {self._active_cells}")

        # Find the key containing the target position value
        target_key = None
        for key, value in self._active_cells.items():
            if key == position:
                target_key = key
                break

        # If we found the key with our target position value
        if target_key is not None:
            logger.debug(f"[DEBUG_REMOVE_MAP] ✅ Found map at position {target_key}")

            # Get map information
            map_model = self._active_cells[target_key]
            map_name = ""

            if map_model and hasattr(map_model, 'get_property'):
                map_name = map_model.get_property('name', '')

            try:
                # Begin remove row
                index = list(self._active_cells.keys()).index(target_key)
                logger.debug(f"[DEBUG_REMOVE_MAP] Removing row at index: {index}")
                self.beginRemoveRows(QModelIndex(), index, index)

                # Remove map from active cells
                if target_key in self._active_cells:
                    # Get map model and dimensions before removing
                    map_model = self._active_cells[target_key]
                    cell_dims = self._cell_dimensions.get(target_key, {"width": 1, "height": 1})

                    # Remove main position
                    logger.debug(f"[DEBUG_REMOVE_MAP] 🗑️ Removing map from active cells")
                    del self._active_cells[target_key]

                    # Remove all related positions (for resized maps)
                    start_row = target_key // self._columns
                    start_col = target_key % self._columns

                    # Remove all occupied positions
                    for row in range(start_row, start_row + cell_dims["height"]):
                        for col in range(start_col, start_col + cell_dims["width"]):
                            occupied_pos = row * self._columns + col
                            if occupied_pos in self._active_cells and self._active_cells[occupied_pos] == map_model:
                                logger.debug(f"[DEBUG_REMOVE_MAP] 🗑️ Removing related position {occupied_pos}")
                                del self._active_cells[occupied_pos]

                # Remove cell dimensions
                if target_key in self._cell_dimensions:
                    logger.debug(f"[DEBUG_REMOVE_MAP] 🗑️ Removing cell dimensions for position {target_key}")
                    del self._cell_dimensions[target_key]

                # End remove row
                self.endRemoveRows()

                # Update next_position
                self._next_position = self._calculate_next_position()

                logger.debug(f"[DEBUG_REMOVE_MAP] ✅ Successfully removed map at position {target_key}")
                logger.debug(f"[DEBUG_REMOVE_MAP] Updated active positions: {sorted(self._active_cells.keys())}")
                logger.debug(f"[DEBUG_REMOVE_MAP] Updated next available position: {self._next_position}")

                # Update grid size based on actual number of items
                if self._auto_resize_enabled:
                    logger.debug(f"[DEBUG_REMOVE_MAP] 🔄 Updating grid size after map removal")
                    # Count actual items (not None)
                    real_count = sum(1 for v in self._active_cells.values() if v is not None)
                    # Update _active_cells to keep only cells with actual items
                    self._active_cells = {k: v for k, v in self._active_cells.items() if v is not None}
                    self._updateGrid()

                # Emit signal to remove UI with empty values
                logger.debug(f"[DEBUG_REMOVE_MAP] 📣 Emitting mapInfoChanged signal for position {target_key} with empty map")
                self.mapInfoChanged.emit(target_key, None)

                return True
            except Exception as inner_e:
                logger.debug(f"[DEBUG_REMOVE_MAP] ❌ Error during map removal operations: {inner_e}")
                logger.debug(f"[DEBUG_REMOVE_MAP] Traceback: {traceback.format_exc()}")
                # Ensure we end the remove rows operation even if an error occurred
                try:
                    self.endRemoveRows()
                except:
                    pass
                return False
        else:
            logger.debug(f"[DEBUG_REMOVE_MAP] ❌ No map found at position {position}")
            return False

    @Slot('QVariantList', result=bool)
    def removeMultipleVideos(self, positions):
        """Remove multiple videos at once"""
        try:
            # Validate positions
            if not positions or len(positions) == 0:
                # logger.debug(f"❌ No positions provided for removal")
                return False

            # Convert positions to integers
            positions = [int(pos) for pos in positions]

            # Get camera names for display
            camera_names = []
            for position in positions:
                if position in self._active_cells:
                    camera_model = self._active_cells[position]
                    if camera_model and hasattr(camera_model, 'data') and camera_model.data:
                        name = camera_model.get_property('name')
                        if name:
                            camera_names.append(name)

            # Import RemoveCameraDialog
            from src.common.widget.dialogs.remove_camera_dialog import RemoveCameraDialog
            from PySide6.QtCore import QCoreApplication

            # Kiểm tra xem có nên hiển thị dialog hay không
            should_show_dialog = RemoveCameraDialog.shouldShow()
            proceed_with_deletion = True

            if should_show_dialog:
                dialog_title = QCoreApplication.translate("GridModel", "Delete Cameras")

                # If we have camera names, show them in the dialog
                if camera_names:
                    # Format camera names with each name on a new line
                    if len(camera_names) <= 10:
                        # Show all camera names if 10 or fewer
                        camera_list = "\n- " + "\n- ".join(camera_names)
                        dialog_description = QCoreApplication.translate("GridModel",
                                                                      "Are you sure you want to delete these cameras?{0}").format(camera_list)
                    else:
                        # If more than 10 cameras, show first 10 and indicate there are more
                        camera_list = "\n- " + "\n- ".join(camera_names[:10])
                        dialog_description = QCoreApplication.translate("GridModel",
                                                                      "Are you sure you want to delete these {0} cameras?{1}\n- ... and {2} more").format(
                                                                          len(positions), camera_list, len(positions) - 10)
                else:
                    # Generic message if no camera names available
                    dialog_description = QCoreApplication.translate("GridModel",
                                                                  "Are you sure you want to delete {0} cameras?").format(len(positions))

                dialog = RemoveCameraDialog(
                    title=dialog_title,
                    description=dialog_description
                )

                # Nếu người dùng xác nhận xóa
                proceed_with_deletion = dialog.exec()

            # Nếu người dùng xác nhận xóa hoặc đã chọn "Không hiển thị lại"
            if proceed_with_deletion:
                success_count = 0

                # Sort positions in reverse order to avoid index shifting during removal
                for position in sorted(positions, reverse=True):
                    try:
                        # Find the key containing the target position value
                        target_key = None
                        for key, value in self._active_cells.items():
                            if key == position:
                                target_key = key
                                break

                        if target_key is not None:
                            # Lấy thông tin camera để hiển thị trong log
                            camera_model = self._active_cells[target_key]
                            camera_name = ""
                            camera_id = None

                            if camera_model and hasattr(camera_model, 'data') and camera_model.data:
                                camera_name = camera_model.get_property('name')
                                camera_id = camera_model.get_property('id')
                                logger.debug(f"Removing camera: {camera_name} at position {target_key}")

                                # Handle video stream removal based on camera usage count across all tabs
                                try:
                                    if camera_id and camera_id in video_capture_controller._list_video_capture:
                                        # Count how many instances of this camera will remain in current grid after removal
                                        remaining_current_grid_count = sum(1 for pos, cam_model in self._active_cells.items()
                                                                         if pos != target_key and cam_model and hasattr(cam_model, 'get_property')
                                                                         and cam_model.get_property('id') == camera_id)

                                        # Check camera usage across all tabs
                                        total_remaining_count = remaining_current_grid_count
                                        try:
                                            # Import để tránh circular import
                                            from src.common.controller.main_controller import main_controller

                                            # Lấy camera usage từ tất cả tab khác
                                            camera_screen = main_controller.list_parent.get('CameraScreen')

                                            if camera_screen and hasattr(camera_screen, 'new_custom_tab_widget'):
                                                tab_widget = camera_screen.new_custom_tab_widget

                                                # Duyệt qua tất cả tab khác
                                                for i in range(tab_widget.tab_widget.tab_bar.count()):
                                                    widget = camera_screen.center_stacked_widget.widget(i)
                                                    if hasattr(widget, 'gridModel') and widget.gridModel != self:  # Skip current tab
                                                        gridModel = widget.gridModel
                                                        other_tab_usage = sum(1 for _, cam_model in gridModel._active_cells.items()
                                                                            if cam_model and hasattr(cam_model, 'get_property')
                                                                            and cam_model.get_property('id') == camera_id)
                                                        total_remaining_count += other_tab_usage

                                            logger.debug(f"Camera {camera_id} remaining: current_grid={remaining_current_grid_count}, total_across_tabs={total_remaining_count}")

                                        except Exception as e:
                                            logger.error(f"Error checking camera usage across tabs: {e}")
                                            # Nếu có lỗi, chỉ dùng current grid usage để tránh unregister nhầm
                                            total_remaining_count = remaining_current_grid_count

                                        # If no other instances will remain across all tabs, unregister completely
                                        if total_remaining_count == 0:
                                            logger.debug(f"Last instance across all tabs - unregistering camera: {camera_id}")
                                            # Get all stream types for this camera
                                            for stream_type, video_capture in list(video_capture_controller._list_video_capture[camera_id].items()):
                                                try:
                                                    video_capture.stop_capture()
                                                except Exception as e:
                                                    logger.error(f"Error stopping capture for camera {camera_id}, stream type {stream_type}: {e}")
                                        else:
                                            # If there are other instances in other tabs, just remove share_frame_signal connection
                                            logger.debug(f"Other instances exist in other tabs - removing share_frame_signal for camera: {camera_id}")
                                except Exception as e:
                                    logger.error(f"Error handling video capture for camera {camera_id}: {e}")

                            # Begin remove row
                            index = list(self._active_cells.keys()).index(target_key)
                            # logger.debug(f"Removing row at index: {index}")
                            self.beginRemoveRows(QModelIndex(), index, index)

                            # Remove video from active cells
                            if target_key in self._active_cells:
                                # Get camera model and dimensions before removing
                                camera_model = self._active_cells[target_key]
                                cell_dims = self._cell_dimensions.get(target_key, {"width": 1, "height": 1})

                                # Remove main position
                                del self._active_cells[target_key]

                                # Remove all related positions (for resized cameras)
                                start_row = target_key // self._columns
                                start_col = target_key % self._columns

                                # Remove all occupied positions
                                for row in range(start_row, start_row + cell_dims["height"]):
                                    for col in range(start_col, start_col + cell_dims["width"]):
                                        occupied_pos = row * self._columns + col
                                        if occupied_pos in self._active_cells and self._active_cells[occupied_pos] == camera_model:
                                            logger.debug(f"Removing related position {occupied_pos}")
                                            del self._active_cells[occupied_pos]

                            if target_key in self._cell_dimensions:
                                del self._cell_dimensions[target_key]
                            # Xóa các thông tin khác liên quan đến camera nếu cần

                            # End remove row
                            self.endRemoveRows()

                            # Emit signal to remove UI with empty values
                            self.videoInfoChanged.emit(target_key, None, False, False)
                            success_count += 1
                    except Exception as inner_e:
                        logger.debug(f"❌ Error removing video at position {position}: {inner_e}")
                        logger.debug(f"Traceback: {traceback.format_exc()}")
                        # Ensure we end the remove rows operation even if an error occurred
                        try:
                            self.endRemoveRows()
                        except:
                            pass

                # Update next_position after all removals
                self._next_position = self._calculate_next_position()

                # logger.debug(f"✅ Successfully removed {success_count} out of {len(positions)} videos")
                # logger.debug(f"Updated active positions: {sorted(self._active_cells.keys())}")
                # logger.debug(f"Updated next available position: {self._next_position}")

                return success_count > 0
            else:
                # User canceled deletion
                # logger.debug(f"User canceled deletion of multiple cameras")
                return False

        except Exception as e:
            logger.debug(f"❌ Error removing multiple videos: {e}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
            return False

    @Slot(str, result=bool)
    def removeCamerasByModel(self, camera_id: str):
        """Remove all cameras with the same model/ID from the grid"""
        try:
            if not camera_id:
                logger.debug(f"❌ Invalid camera_id: {camera_id}")
                return False

            logger.debug(f"🔄 [DEBUG_REMOVE_BY_MODEL] Starting removal of all cameras with ID: {camera_id}")

            # Find all positions with cameras that have the same ID
            positions_to_remove = []
            camera_names = []

            for position, camera_model in self._active_cells.items():
                if camera_model and hasattr(camera_model, 'get_property'):
                    current_camera_id = camera_model.get_property('id')
                    if current_camera_id == camera_id:
                        positions_to_remove.append(position)
                        camera_name = camera_model.get_property('name', '')
                        if camera_name and camera_name not in camera_names:
                            camera_names.append(camera_name)

            if not positions_to_remove:
                logger.debug(f"❌ No cameras found with ID: {camera_id}")
                return False

            logger.debug(f"🔄 [DEBUG_REMOVE_BY_MODEL] Found {len(positions_to_remove)} cameras to remove at positions: {positions_to_remove}")

            # Import RemoveCameraDialog
            from src.common.widget.dialogs.remove_camera_dialog import RemoveCameraDialog
            from PySide6.QtCore import QCoreApplication

            # Check if dialog should be shown
            should_show_dialog = RemoveCameraDialog.shouldShow()
            proceed_with_deletion = True

            if should_show_dialog:
                dialog_title = QCoreApplication.translate("GridModel", "Delete All Cameras of Same Model")

                # Create description based on camera names
                if camera_names:
                    camera_name = camera_names[0]  # Use first camera name since they're all the same model
                    dialog_description = QCoreApplication.translate("GridModel",
                        "Are you sure you want to delete all {0} cameras of model '{1}'?").format(
                        len(positions_to_remove), camera_name)
                else:
                    dialog_description = QCoreApplication.translate("GridModel",
                        "Are you sure you want to delete all {0} cameras of this model?").format(len(positions_to_remove))

                dialog = RemoveCameraDialog(
                    title=dialog_title,
                    description=dialog_description
                )

                proceed_with_deletion = dialog.exec()

            if proceed_with_deletion:
                # Use existing removeMultipleVideos method to handle the actual removal
                return self.removeMultipleVideos(positions_to_remove)
            else:
                logger.debug(f"User canceled deletion of cameras with model ID: {camera_id}")
                return False

        except Exception as e:
            logger.debug(f"❌ Error removing cameras by model: {e}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
            return False

    @Slot(int, result=bool)
    def removeCamerasByModelFromPosition(self, position: int):
        """Remove all cameras with the same model/ID as the camera at the specified position"""
        try:
            if position is None or not isinstance(position, int) or position < 0:
                logger.debug(f"❌ Invalid position: {position}")
                return False

            if position not in self._active_cells:
                logger.debug(f"❌ No camera found at position: {position}")
                return False

            camera_model = self._active_cells[position]
            if not camera_model or not hasattr(camera_model, 'get_property'):
                logger.debug(f"❌ Invalid camera model at position: {position}")
                return False

            camera_id = camera_model.get_property('id')
            if not camera_id:
                logger.debug(f"❌ No camera ID found at position: {position}")
                return False

            logger.debug(f"🔄 [DEBUG_REMOVE_BY_MODEL_FROM_POS] Removing all cameras with same model as position {position} (ID: {camera_id})")

            # Use the removeCamerasByModel method
            return self.removeCamerasByModel(camera_id)

        except Exception as e:
            logger.debug(f"❌ Error removing cameras by model from position: {e}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
            return False

    def removeAllItem(self, check_other_tabs=True):
        """Remove all cameras from the grid and close video streams if not used in other tabs"""
        logger.debug('removeAllItem - Closing video streams (checking other tabs)')

        try:
            # Lưu danh sách các camera_id từ tab hiện tại
            camera_ids_in_current_tab = []

            # Thu thập tất cả camera_id từ active_cells
            for position, camera_model in self._active_cells.items():
                if camera_model and hasattr(camera_model, 'get_property'):
                    camera_id = camera_model.get_property('id')
                    if camera_id and camera_id not in camera_ids_in_current_tab:
                        camera_ids_in_current_tab.append(camera_id)
                        logger.debug(f"Adding camera ID {camera_id} from current tab")

            # Nếu cần kiểm tra tab khác, tìm camera usage từ tất cả tab
            cameras_to_unregister = set(camera_ids_in_current_tab)

            if check_other_tabs:
                try:
                    # Import để tránh circular import
                    from src.common.controller.main_controller import main_controller

                    # Lấy camera usage từ tất cả tab khác
                    cameras_in_other_tabs = set()
                    camera_screen = main_controller.list_parent.get('CameraScreen')

                    if camera_screen and hasattr(camera_screen, 'new_custom_tab_widget'):
                        tab_widget = camera_screen.new_custom_tab_widget

                        # Duyệt qua tất cả tab
                        for i in range(tab_widget.tab_widget.tab_bar.count()):
                            widget = camera_screen.center_stacked_widget.widget(i)
                            if hasattr(widget, 'gridModel') and widget.gridModel != self:  # Skip current tab
                                gridModel = widget.gridModel
                                for _, camera_model in gridModel._active_cells.items():
                                    if camera_model and hasattr(camera_model, 'get_property'):
                                        camera_id = camera_model.get_property('id')
                                        if camera_id:
                                            cameras_in_other_tabs.add(camera_id)

                    # Chỉ unregister camera không có ở tab khác
                    cameras_to_unregister = set(camera_ids_in_current_tab) - cameras_in_other_tabs
                    logger.debug(f"Cameras in current tab: {camera_ids_in_current_tab}")
                    logger.debug(f"Cameras in other tabs: {cameras_in_other_tabs}")
                    logger.debug(f"Cameras to unregister: {cameras_to_unregister}")

                except Exception as e:
                    logger.error(f"Error checking other tabs, will unregister all: {e}")
                    # Nếu có lỗi khi check tab khác, vẫn unregister tất cả để tránh memory leak
                    cameras_to_unregister = set(camera_ids_in_current_tab)

            # Đóng video streams cho camera cần unregister
            for camera_id in cameras_to_unregister:
                try:
                    # Kiểm tra xem camera_id có trong list_video_capture không
                    if camera_id in video_capture_controller._list_video_capture:
                        logger.debug(f"Unregistering camera ID: {camera_id} (not used in other tabs)")

                        # Đóng tất cả các loại stream của camera này
                        for stream_type, video_capture in list(video_capture_controller._list_video_capture[camera_id].items()):
                            try:
                                video_capture.stop_capture()
                            except Exception as e:
                                logger.error(f"Error stopping capture for camera {camera_id}, stream type {stream_type}: {e}")
                except Exception as e:
                    logger.error(f"Error closing streams for camera {camera_id}: {e}")

            # Log camera chỉ remove share_frame_signal (vẫn được dùng ở tab khác)
            cameras_keep_running = set(camera_ids_in_current_tab) - cameras_to_unregister
            for camera_id in cameras_keep_running:
                logger.debug(f"Keeping camera ID: {camera_id} running (used in other tabs)")

            # Xóa tất cả các camera khỏi grid
            self._active_cells.clear()
            self._cell_dimensions.clear()

            # Cập nhật grid về kích thước mặc định
            if self._auto_resize_enabled:
                self._columns = 1
                self._rows = 1
                self.columnsChanged.emit(self._columns)
                self.rowsChanged.emit(self._rows)
                self.gridChanged.emit(self._columns, self._rows)

            logger.debug(f"Successfully removed all items and closed all video streams")
        except Exception as e:
            logger.error(f"Error in removeAllItem: {e}")
            logger.error(traceback.format_exc())

        # Phát thông báo sang QML để đóng luồng videocapture và widget
        self.removeAllItemChanged.emit()
    @Slot(int, result=bool)
    def isActivePosition(self, position: int):
        """Check if a position is currently active"""
        return position in self._active_cells

    @Slot(int, result='QVariantList')
    def findAvailablePositions(self, count):
        """Find available positions that are not occupied by any camera (including resized cameras)"""
        try:
            available_positions = []
            max_position = self._rows * self._columns

            for position in range(max_position):
                # Position is available if it's not in _active_cells or value is None
                if position not in self._active_cells or self._active_cells[position] is None:
                    available_positions.append(position)
                    if len(available_positions) >= count:
                        break

            logger.debug(f"Found {len(available_positions)} available positions (requested {count}): {available_positions}")

            # Debug: Log current _active_cells state when finding available positions
            self._debug_log_active_cells_state(f"Finding {count} available positions")

            return available_positions

        except Exception as e:
            logger.error(f"Error finding available positions: {e}")
            return []

    @Slot(int)
    def getVideoInfo(self, position):
        """Lấy thông tin video tại một vị trí"""
        if position in self._active_cells:
            camera_model = self._active_cells[position]
            if camera_model and hasattr(camera_model, 'data') and camera_model.data:
                return {
                    "url": camera_model.get_property("urlMainstream"),
                    "name": camera_model.get_property('name'),
                    "isPlaying": True  # This is just for UI purposes
                }
        return {"url": "", "name": "", "isPlaying": False}

    def _updateGrid(self):
        """Update grid layout based on number of videos"""
        count = len(self._active_cells)
        old_cols = self._columns
        old_rows = self._rows

        # Đảm bảo rằng nếu không có camera nào, vẫn có thể thay đổi về 1x1
        if count == 0:
            # Nếu không có camera nào, vẫn giữ nguyên kích thước hiện tại
            # hoặc đặt lại thành 1x1 nếu đang ở chế độ auto-resize
            if self._auto_resize_enabled:
                self._columns = 1
                self._rows = 1
        elif count == 1:
            self._columns = 1
            self._rows = 1
        elif count == 2:
            self._columns = 2
            self._rows = 1
        elif count == 3 or count == 4:
            self._columns = 2
            self._rows = 2
        elif count <= 6:
            self._columns = 3
            self._rows = 2
        elif count <= 9:
            self._columns = 3
            self._rows = 3
        elif count <= 12:
            self._columns = 4
            self._rows = 3
        elif count <= 16:
            self._columns = 4
            self._rows = 4
        elif count <= 20:
            self._columns = 5
            self._rows = 4
        elif count <= 25:
            self._columns = 5
            self._rows = 5
        elif count <= 30:
            self._columns = 6
            self._rows = 5
        elif count <= 36:
            self._columns = 6
            self._rows = 6
        elif count <= 42:
            self._columns = 7
            self._rows = 6
        elif count <= 49:
            self._columns = 7
            self._rows = 7
        elif count <= 56:
            self._columns = 8
            self._rows = 7
        elif count <= 64:
            self._columns = 8
            self._rows = 8
        elif count <= 72:
            self._columns = 9
            self._rows = 8
        elif count <= 81:
            self._columns = 9
            self._rows = 9
        elif count <= 90:
            self._columns = 10
            self._rows = 9
        elif count <= 100:
            self._columns = 10
            self._rows = 10
        elif count <= 110:
            self._columns = 11
            self._rows = 10
        elif count <= 121:
            self._columns = 11
            self._rows = 11
        else:
            # Giới hạn tối đa 12x12 = 144 camera
            self._columns = 12
            self._rows = 12

        self._layout = list(range(count))

        # Emit signals
        if old_cols != self._columns:
            self.columnsChanged.emit(self._columns)
        if old_rows != self._rows:
            self.rowsChanged.emit(self._rows)

        print(f"Grid updated: {count} videos, {self._columns}x{self._rows} grid")
        self.gridChanged.emit(self._columns, self._rows)

        # Không thêm các ô trống vào _active_cells
        # Chỉ đếm các ô có camera thực sự
        active_count = sum(1 for v in self._active_cells.values() if v is not None)
        print(f"Active cells count: {active_count}")
    @Property(int, notify=columnsChanged)
    def columns(self):
        return self._columns

    @columns.setter
    def columns(self, value):
        if self._columns != value:
            self._columns = value
            self.columnsChanged.emit(value)
            self._updateLayout()

    @Property(QObject)
    def gridItemSelected(self):
        return self._grid_item_selected


    @Property(int, notify=rowsChanged)
    def rows(self):
        return self._rows

    @rows.setter
    def rows(self, value):
        if self._rows != value:
            self._rows = value
            self.rowsChanged.emit(value)
            self._updateLayout()

    @Property(int)
    def previousColumns(self):
        """Trả về số cột trước khi maximize"""
        return self._previous_columns

    @Property(int)
    def previousRows(self):
        """Trả về số hàng trước khi maximize"""
        return self._previous_rows

    @Property(int)
    def nextPosition(self):
        return self._next_position

    @Property('QVariantList')
    def layout(self):
        return self._layout

    @Slot(result='QVariantList')
    def getActivePositions(self):
        """Trả về danh sách các vị trí đang có video, đã được sắp xếp"""
        return sorted(list(self._active_cells))

    def getGridPosition(self, index):
        """Get row and column for a position in the grid"""
        if index >= len(self._layout):
            return -1, -1
        pos = self._layout[index]
        row = pos // self._columns
        col = pos % self._columns
        return row, col

    @Property(bool, notify=isMaximizedChanged)
    def isMaximized(self):
        return self._is_maximized

    @Slot(int)
    def maximizeGrid(self, position):
        """Phóng to toàn bộ lưới"""
        print(f"🔍 [MAXIMIZE] maximizeGrid called for position {position}")
        print(f"🔍 [MAXIMIZE] Current state - is_maximized: {self._is_maximized}")

        if not self._is_maximized:  # Only maximize if not already maximized
            print(f"🔍 [MAXIMIZE] Proceeding with maximize...")
            self._previous_columns = self._columns
            self._previous_rows = self._rows
            self._active_item_position = position  # Set position first

            print(f"🔍 [MAXIMIZE] Saving previous state: columns={self._previous_columns}, rows={self._previous_rows}")
            print(f"🔍 [MAXIMIZE] Setting active position to: {position}")

            self.activeItemPositionChanged.emit(self._active_item_position)

            self._is_maximized = True  # Then set maximized state
            print(f"🔍 [MAXIMIZE] Setting is_maximized to True")
            self.isMaximizedChanged.emit(self._is_maximized)

            self._columns = 1  # Finally update grid size
            self._rows = 1
            print(f"🔍 [MAXIMIZE] Setting grid size to 1x1")

            self.columnsChanged.emit(self._columns)
            self.rowsChanged.emit(self._rows)
            print(f"✅ [MAXIMIZE] Grid maximized successfully. Active position: {position}")
            logger.debug(f"Grid maximized to full size. Active position: {position}")
        else:
            print(f"❌ [MAXIMIZE] Already maximized, skipping")

    @Slot()
    def restoreGrid(self):
        """Khôi phục kích thước lưới về trạng thái bình thường"""
        print(f"🔍 [RESTORE] restoreGrid called")
        print(f"🔍 [RESTORE] Current state - is_maximized: {self._is_maximized}")

        if self._is_maximized:  # Only restore if currently maximized
            print(f"🔍 [RESTORE] Proceeding with restore...")
            # Lưu lại vị trí của camera đang ở chế độ fullscreen
            active_position = self._active_item_position
            print(f"🔍 [RESTORE] Active position: {active_position}")
            print(f"🔍 [RESTORE] Previous grid size: {self._previous_columns}x{self._previous_rows}")

            # Phát tín hiệu để QML biết rằng animation restore đang bắt đầu
            # Điều này cho phép QML đặt z-index lớn nhất cho animation
            if active_position is not None:
                print(f"🔍 [RESTORE] Emitting restoreAnimationStarted signal for position: {active_position}")
                logger.debug(f"Emitting restoreAnimationStarted signal for position: {active_position}")
                self.restoreAnimationStarted.emit(active_position)

            # Đợi một chút để QML có thể xử lý tín hiệu trước khi thay đổi trạng thái
            # Không cần sleep vì QML sẽ xử lý tín hiệu ngay lập tức

            self._is_maximized = False  # First clear maximized state
            print(f"🔍 [RESTORE] Setting is_maximized to False")
            self.isMaximizedChanged.emit(self._is_maximized)

            self._active_item_position = None  # Then clear active position
            print(f"🔍 [RESTORE] Clearing active position")
            self.activeItemPositionChanged.emit(self._active_item_position)

            self._columns = self._previous_columns  # Finally restore grid size
            self._rows = self._previous_rows
            self._previous_columns = self._columns
            self._previous_rows = self._rows
            print(f"🔍 [RESTORE] Restoring grid size to: {self._columns}x{self._rows}")
            self.columnsChanged.emit(self._columns)
            self.rowsChanged.emit(self._rows)
            print(f"✅ [RESTORE] Grid restored successfully to normal size")
            logger.debug("Grid restored to normal size.")
        else:
            print(f"❌ [RESTORE] Not maximized, skipping restore")

    @Property(int, notify=activeItemPositionChanged)
    def activeItemPosition(self):
        return self._active_item_position

    def _calculate_next_position(self):
        """Tính toán vị trí trống nhỏ nhất"""
        if not self._active_cells:
            return 0

        # Tìm vị trí trống nhỏ nhất
        sorted_positions = sorted(self._active_cells.keys())
        next_pos = 0
        for pos in sorted_positions:
            if next_pos < pos:  # Nếu tìm thấy khoảng trống
                break
            next_pos = pos + 1
        logger.debug(f"Next available position: {next_pos}")
        return next_pos

    def _updateLayout(self):
        """Update the layout and emit layoutChanged signal"""
        # Get all active positions
        active_positions = sorted(list(self._active_cells.keys()))

        # Update layout list
        self._layout = active_positions

        # Emit layoutChanged signal with active positions
        self.layoutChanged.emit(active_positions)

        logger.debug(f"Layout updated with positions: {active_positions}")

    @Slot(int, int)
    def updateVideoPosition(self, old_pos: int, new_pos: int):
        """Update video position when moved to empty cell"""
        try:
            if old_pos in self._active_cells:
                # Store the grid item position and dimensions
                grid_item_pos = self._active_cells[old_pos]
                cell_dims = self._cell_dimensions.get(old_pos, {"width": 1, "height": 1})

                # Get camera model before clearing old positions
                camera_model = self._active_cells[old_pos]

                # Clear old occupied positions
                old_start_row = old_pos // self._columns
                old_start_col = old_pos % self._columns
                for row in range(old_start_row, old_start_row + cell_dims["height"]):
                    for col in range(old_start_col, old_start_col + cell_dims["width"]):
                        old_occupied_pos = row * self._columns + col
                        if old_occupied_pos in self._active_cells and self._active_cells[old_occupied_pos] == camera_model:
                            logger.debug(f"Removing old occupied position {old_occupied_pos}")
                            del self._active_cells[old_occupied_pos]

                # Update main position
                self._active_cells[new_pos] = camera_model

                # Mark new occupied positions
                new_start_row = new_pos // self._columns
                new_start_col = new_pos % self._columns
                for row in range(new_start_row, new_start_row + cell_dims["height"]):
                    for col in range(new_start_col, new_start_col + cell_dims["width"]):
                        new_occupied_pos = row * self._columns + col
                        if new_occupied_pos != new_pos:
                            # Đánh dấu ô này thuộc về camera_model, không phải new_pos
                            self._active_cells[new_occupied_pos] = camera_model

                # Update cell dimensions
                if old_pos in self._cell_dimensions:
                    self._cell_dimensions[new_pos] = self._cell_dimensions[old_pos]
                    del self._cell_dimensions[old_pos]
                    # Emit signal to update UI
                    self.cellDimensionsChanged.emit(new_pos, cell_dims["width"], cell_dims["height"])

                # Update next_position
                self._next_position = self._calculate_next_position()

                print(f"Updated video position from {old_pos} to {new_pos}")
                print(f"Active positions: {sorted(self._active_cells.keys())}")
                print(f"Next available position: {self._next_position}")
                return True

        except Exception as e:
            print(f"Error updating video position: {e}")
            return False

    @Slot(int, int)
    def swapPositions(self, source_pos: int, target_pos: int):
        """Swap positions between two items (cameras, maps, etc.)"""
        if source_pos == target_pos:
            return True

        try:
            if source_pos in self._active_cells and target_pos in self._active_cells:
                print(f"[SWAP_DEBUG] Bắt đầu swap giữa vị trí {source_pos} và {target_pos}")
                print(f"[SWAP_DEBUG] Active cells trước khi swap: {self._active_cells}")
                print(f"[SWAP_DEBUG] Cell dimensions: {self._cell_dimensions}")

                # Kiểm tra xem source_pos hoặc target_pos có phải là camera resize không
                source_is_resize = False
                target_is_resize = False

                # Kiểm tra source_pos
                if source_pos in self._cell_dimensions:
                    source_dims = self._cell_dimensions[source_pos]
                    if source_dims["width"] > 1 or source_dims["height"] > 1:
                        source_is_resize = True
                        print(f"[SWAP_DEBUG] Source position {source_pos} là item resize với kích thước {source_dims}")

                # Kiểm tra target_pos
                if target_pos in self._cell_dimensions:
                    target_dims = self._cell_dimensions[target_pos]
                    if target_dims["width"] > 1 or target_dims["height"] > 1:
                        target_is_resize = True
                        print(f"[SWAP_DEBUG] Target position {target_pos} là item resize với kích thước {target_dims}")

                # Lưu giá trị item và kích thước
                source_value = self._active_cells[source_pos]
                target_value = self._active_cells[target_pos]
                source_dims = self._cell_dimensions.get(source_pos, {"width": 1, "height": 1})
                target_dims = self._cell_dimensions.get(target_pos, {"width": 1, "height": 1})

                print(f"[SWAP_DEBUG] Source value: {source_value}, Target value: {target_value}")

                # Tạo bản sao của active_cells để lưu trữ trạng thái ban đầu
                self._original_active_cells = self._active_cells.copy()
                print(f"[SWAP_DEBUG] Đã lưu trạng thái ban đầu của active_cells")

                # Xóa các ô phụ của item resize
                cells_to_remove = []

                # Tìm các ô phụ của source item resize
                if source_is_resize:
                    start_row = source_pos // self._columns
                    start_col = source_pos % self._columns
                    for row in range(start_row, start_row + source_dims["height"]):
                        for col in range(start_col, start_col + source_dims["width"]):
                            occupied_pos = row * self._columns + col
                            # Chỉ xóa các ô phụ của item resize, không xóa các item có cùng ID
                            if occupied_pos != source_pos and occupied_pos in self._active_cells and self._active_cells[occupied_pos] == source_value:
                                cells_to_remove.append(occupied_pos)
                                print(f"[SWAP_DEBUG] Đánh dấu xóa ô phụ {occupied_pos} thuộc về source item resize")

                # Tìm các ô phụ của target item resize
                if target_is_resize:
                    start_row = target_pos // self._columns
                    start_col = target_pos % self._columns
                    for row in range(start_row, start_row + target_dims["height"]):
                        for col in range(start_col, start_col + target_dims["width"]):
                            occupied_pos = row * self._columns + col
                            # Chỉ xóa các ô phụ của item resize, không xóa các item có cùng ID
                            if occupied_pos != target_pos and occupied_pos in self._active_cells and self._active_cells[occupied_pos] == target_value:
                                cells_to_remove.append(occupied_pos)
                                print(f"[SWAP_DEBUG] Đánh dấu xóa ô phụ {occupied_pos} thuộc về target item resize")

                # Xóa các ô phụ đã đánh dấu
                for pos in cells_to_remove:
                    if pos in self._active_cells:
                        del self._active_cells[pos]
                        print(f"[SWAP_DEBUG] Đã xóa ô phụ {pos} khỏi active_cells")

                print(f"[SWAP_DEBUG] Active cells sau khi xóa các ô bị chiếm: {self._active_cells}")

                # Swap item models
                self._active_cells[source_pos] = target_value
                self._active_cells[target_pos] = source_value
                print(f"[SWAP_DEBUG] Đã swap item models giữa {source_pos} và {target_pos}")

                # Đánh dấu lại các ô bị chiếm bởi item resize sau khi swap
                if source_is_resize:
                    start_row = source_pos // self._columns
                    start_col = source_pos % self._columns

                    print(f"[SWAP_DEBUG] Đánh dấu lại các ô bị chiếm bởi source item resize tại {source_pos}")
                    for row in range(start_row, start_row + source_dims["height"]):
                        for col in range(start_col, start_col + source_dims["width"]):
                            occupied_pos = row * self._columns + col
                            if occupied_pos != source_pos:
                                # Chỉ đánh dấu nếu ô này chưa bị chiếm bởi item khác
                                if occupied_pos not in self._active_cells:
                                    self._active_cells[occupied_pos] = target_value
                                    print(f"[SWAP_DEBUG] Đánh dấu ô trống {occupied_pos} thuộc về target item {target_value}")

                if target_is_resize:
                    start_row = target_pos // self._columns
                    start_col = target_pos % self._columns

                    print(f"[SWAP_DEBUG] Đánh dấu lại các ô bị chiếm bởi target item resize tại {target_pos}")
                    for row in range(start_row, start_row + target_dims["height"]):
                        for col in range(start_col, start_col + target_dims["width"]):
                            occupied_pos = row * self._columns + col
                            if occupied_pos != target_pos:
                                # Chỉ đánh dấu nếu ô này chưa bị chiếm bởi item khác
                                if occupied_pos not in self._active_cells:
                                    self._active_cells[occupied_pos] = source_value
                                    print(f"[SWAP_DEBUG] Đánh dấu ô trống {occupied_pos} thuộc về source item {source_value}")

                print(f"[SWAP_DEBUG] Active cells sau khi đánh dấu lại: {self._active_cells}")

                # Kiểm tra xem có ô nào bị trùng lặp không
                all_positions = set()
                duplicate_positions = set()

                for pos in self._active_cells.keys():
                    if pos in all_positions:
                        duplicate_positions.add(pos)
                        print(f"[SWAP_DEBUG] Phát hiện vị trí trùng lặp: {pos}")
                    else:
                        all_positions.add(pos)

                # Nếu có ô bị trùng lặp, khôi phục lại trạng thái ban đầu
                if duplicate_positions:
                    logger.error(f"Duplicate positions detected after swap: {duplicate_positions}")
                    print(f"[SWAP_DEBUG] Phát hiện vị trí trùng lặp: {duplicate_positions}, khôi phục lại trạng thái ban đầu")
                    self._active_cells = self._original_active_cells
                    return False

                # Determine the type of items being swapped
                source_is_map = False
                target_is_map = False

                # Check if source is a map
                if isinstance(source_value, str):
                    # If it's a string ID, we need to check if it's a map ID
                    source_map_model = map_manager.get_map_model(id=source_value)
                    source_floor_model = floor_manager.get_floor(id=source_value)
                    source_is_map = source_map_model is not None or source_floor_model is not None
                elif hasattr(source_value, 'data'):
                    # If it's an object with data attribute, check its type
                    if hasattr(source_value.data, 'listBuildings') or hasattr(source_value.data, 'listFloor'):
                        source_is_map = True

                # Check if target is a map
                if isinstance(target_value, str):
                    # If it's a string ID, we need to check if it's a map ID
                    target_map_model = map_manager.get_map_model(id=target_value)
                    target_floor_model = floor_manager.get_floor(id=target_value)
                    target_is_map = target_map_model is not None or target_floor_model is not None
                elif hasattr(target_value, 'data'):
                    # If it's an object with data attribute, check its type
                    if hasattr(target_value.data, 'listBuildings') or hasattr(target_value.data, 'listFloor'):
                        target_is_map = True

                # Emit appropriate signals based on item types
                if source_is_map:
                    # Source is a map, emit mapInfoChanged
                    if isinstance(source_value, str):
                        # If it's a string ID, get the model
                        source_model = map_manager.get_map_model(id=source_value)
                        if source_model is None:
                            source_model = floor_manager.get_floor(id=source_value)
                    else:
                        source_model = source_value
                    self.mapInfoChanged.emit(target_pos, source_model)
                else:
                    # Source is a camera, emit videoInfoChanged
                    source_supports_ptz = False
                    if hasattr(source_value, "get_property"):
                        # Check if source camera supports PTZ
                        source_ptz_caps = source_value.get_property("ptzCap", [])
                        source_supports_ptz = source_ptz_caps is not None and len(source_ptz_caps) > 0
                    self.videoInfoChanged.emit(target_pos, source_value, True, source_supports_ptz)

                if target_is_map:
                    # Target is a map, emit mapInfoChanged
                    if isinstance(target_value, str):
                        # If it's a string ID, get the model
                        target_model = map_manager.get_map_model(id=target_value)
                        if target_model is None:
                            target_model = floor_manager.get_floor(id=target_value)
                    else:
                        target_model = target_value
                    self.mapInfoChanged.emit(source_pos, target_model)
                else:
                    # Target is a camera, emit videoInfoChanged
                    target_supports_ptz = False
                    if hasattr(target_value, "get_property"):
                        # Check if target camera supports PTZ
                        target_ptz_caps = target_value.get_property("ptzCap", [])
                        target_supports_ptz = target_ptz_caps is not None and len(target_ptz_caps) > 0
                    self.videoInfoChanged.emit(source_pos, target_value, True, target_supports_ptz)

                # Emit cellDimensionsChanged signals for both positions after swap
                # This ensures GridItem.qml knows about the new dimensions at each position
                source_final_dims = self._cell_dimensions.get(source_pos, {"width": 1, "height": 1})
                target_final_dims = self._cell_dimensions.get(target_pos, {"width": 1, "height": 1})

                print(f"[SWAP_DEBUG] Emitting cellDimensionsChanged for source_pos {source_pos}: {source_final_dims}")
                self.cellDimensionsChanged.emit(source_pos, source_final_dims["width"], source_final_dims["height"])

                print(f"[SWAP_DEBUG] Emitting cellDimensionsChanged for target_pos {target_pos}: {target_final_dims}")
                self.cellDimensionsChanged.emit(target_pos, target_final_dims["width"], target_final_dims["height"])

                # Emit signals to update UI for both positions
                logger.debug(f"Swapped positions between {source_pos} and {target_pos}")
                print(f"[SWAP_DEBUG] Hoàn thành swap giữa vị trí {source_pos} và {target_pos}")
                print(f"[SWAP_DEBUG] Active cells sau khi swap: {self._active_cells}")
                return True

            return False

        except Exception as e:
            logger.debug(f"Error swapping positions: {e}")
            logger.debug(f"Error details: {traceback.format_exc()}")
            print(f"[SWAP_DEBUG] Lỗi khi swap: {e}")
            print(f"[SWAP_DEBUG] Chi tiết lỗi: {traceback.format_exc()}")
            return False


    @Slot(int, int)
    def setGridSize(self, columns, rows):
        """Set grid size manually"""
        if columns != self._columns or rows != self._rows:
            # Đảm bảo giá trị nằm trong khoảng hợp lệ
            self._columns = max(1, min(12, columns))
            self._rows = max(1, min(12, rows))

            logger.debug(f"Grid size changed to {self._columns}x{self._rows}")

            # Lưu trữ giá trị trước đó để có thể khôi phục khi cần
            if not self._is_maximized:
                self._previous_columns = self._columns
                self._previous_rows = self._rows

            # Phát tín hiệu thay đổi
            self.columnsChanged.emit(self._columns)
            self.rowsChanged.emit(self._rows)

            # Emit cellDimensionsChanged for all existing cameras
            # so they can update their visual size based on new grid dimensions
            for pos, dimensions in self._cell_dimensions.items():
                if pos in self._active_cells:  # Only for active cameras
                    self.cellDimensionsChanged.emit(pos, dimensions["width"], dimensions["height"])

            # Cập nhật layout nếu cần
            self._updateLayout()

            # Disable auto-resize when manually setting grid size
            self._auto_resize_enabled = False

            # Không cần phát tín hiệu layoutChanged vì đã gọi _updateLayout

    @Slot(int, result='QVariant')
    def calculateOptimalGridSize(self, totalCameras):
        """Tính toán kích thước grid tối ưu cho số camera cho trước"""
        cols, rows = self._calculateGridSize(totalCameras)
        return {"columns": cols, "rows": rows}

    @Slot(int, int, int)# Signal for cell dimension changes
    def updateCellDimensions(self, position, width_cells, height_cells):
        """Update the dimensions of a cell in terms of grid cells

        Args:
            position: The position of the cell in the grid
            width_cells: Width in number of grid cells
            height_cells: Height in number of grid cells
        """
        if position in self._active_cells:
            # Validate and ensure dimensions are within reasonable bounds
            width_cells = max(1, min(12, width_cells))
            height_cells = max(1, min(12, height_cells))

            # Log current state before update
            current_dims = self._cell_dimensions.get(position, {"width": 1, "height": 1})
            logger.debug(f"Current dimensions at position {position}: {current_dims['width']}x{current_dims['height']} cells")
            logger.debug(f"Updating to: {width_cells}x{height_cells} cells")

            # Update dimensions
            self._cell_dimensions[position] = {
                "width": width_cells,
                "height": height_cells
            }

            # Đánh dấu các ô bị chiếm bởi camera resize
            start_row = position // self._columns
            start_col = position % self._columns

            # Xóa các ô đã đánh dấu trước đó (nếu có)
            keys_to_remove = [key for key, value in self._active_cells.items()
                            if value == position and key != position]
            for key in keys_to_remove:
                del self._active_cells[key]

            # Đánh dấu các ô mới bị chiếm
            camera_model = self._active_cells[position]
            for row in range(start_row, start_row + height_cells):
                for col in range(start_col, start_col + width_cells):
                    occupied_pos = row * self._columns + col
                    if occupied_pos != position:
                        # Chỉ đánh dấu nếu ô này chưa bị chiếm bởi camera khác
                        # hoặc đã thuộc về camera này
                        if occupied_pos not in self._active_cells or self._active_cells[occupied_pos] == camera_model:
                            self._active_cells[occupied_pos] = camera_model
                            logger.debug(f"Marking secondary cell {occupied_pos} for camera at {position}")

            # Cập nhật next_position sau khi thay đổi kích thước
            self._next_position = self._calculate_next_position()

            # Cập nhật layout
            self._updateLayout()

            # Emit signal for dimension changes
            self.cellDimensionsChanged.emit(position, width_cells, height_cells)

            # Log final state
            logger.debug(f"✅ Updated cell dimensions at position {position}: {width_cells}x{height_cells} cells")
            logger.debug(f"Next available position: {self._next_position}")

            # Debug: Log current _active_cells state
            self._debug_log_active_cells_state(f"After resize {position} to {width_cells}x{height_cells}")

            return True
        return False

    @Slot(int, int, int, result=bool)
    def canResizeToSize(self, position, width_cells, height_cells):
        """Check if a cell can be resized to the specified dimensions without conflicts

        This method is exposed to QML to provide accurate resize conflict checking
        that considers both primary and secondary positions from resized cameras.

        Args:
            position: The position of the cell in the grid
            width_cells: Desired width in number of grid cells
            height_cells: Desired height in number of grid cells

        Returns:
            bool: True if resize is possible, False if there are conflicts
        """
        return self._can_resize_to_size(position, width_cells, height_cells)

    def _can_resize_to_size(self, position, width_cells, height_cells):
        """Internal method to check if a cell can be resized to the specified dimensions without conflicts

        Args:
            position: The position of the cell in the grid
            width_cells: Desired width in number of grid cells
            height_cells: Desired height in number of grid cells

        Returns:
            bool: True if resize is possible, False if there are conflicts
        """
        start_row = position // self._columns
        start_col = position % self._columns

        # Kiểm tra xem kích thước mới có vượt quá lưới không
        if start_row + height_cells > self._rows or start_col + width_cells > self._columns:
            logger.warning(f"Resize would exceed grid boundaries: {width_cells}x{height_cells} at position {position}")
            return False

        # Get the camera model at the current position
        current_camera = self._active_cells.get(position)
        if not current_camera:
            logger.warning(f"No camera found at position {position}")
            return False

        # Kiểm tra xem có xung đột với camera khác không
        for row in range(start_row, start_row + height_cells):
            for col in range(start_col, start_col + width_cells):
                check_pos = row * self._columns + col

                # Bỏ qua vị trí hiện tại
                if check_pos == position:
                    continue

                # Kiểm tra xem ô có bị chiếm bởi camera khác không
                if check_pos in self._active_cells:
                    occupying_camera = self._active_cells[check_pos]
                    # Nếu ô này không thuộc về camera hiện tại (so sánh camera model objects)
                    if occupying_camera != current_camera:
                        logger.warning(f"Position {check_pos} is occupied by another camera")
                        return False

        return True

    def _debug_log_active_cells_state(self, context=""):
        """Debug method to log current _active_cells state"""
        try:
            logger.debug(f"🔍 [DEBUG_ACTIVE_CELLS] {context}")
            logger.debug(f"🔍 [DEBUG_ACTIVE_CELLS] Grid size: {self._columns}x{self._rows}")
            logger.debug(f"🔍 [DEBUG_ACTIVE_CELLS] Total active cells: {len(self._active_cells)}")

            # Group positions by camera model
            camera_positions = {}
            for pos, camera_model in self._active_cells.items():
                if camera_model:
                    camera_id = camera_model.get_property('id') if hasattr(camera_model, 'get_property') else str(camera_model)
                    if camera_id not in camera_positions:
                        camera_positions[camera_id] = []
                    camera_positions[camera_id].append(pos)

            # Log each camera and its positions
            for camera_id, positions in camera_positions.items():
                primary_pos = min(positions)  # Primary position is the smallest
                secondary_positions = [pos for pos in positions if pos != primary_pos]
                dimensions = self._cell_dimensions.get(primary_pos, {"width": 1, "height": 1})

                logger.debug(f"🔍 [DEBUG_ACTIVE_CELLS] Camera {camera_id}:")
                logger.debug(f"🔍 [DEBUG_ACTIVE_CELLS]   Primary position: {primary_pos}")
                logger.debug(f"🔍 [DEBUG_ACTIVE_CELLS]   Dimensions: {dimensions['width']}x{dimensions['height']}")
                if secondary_positions:
                    logger.debug(f"🔍 [DEBUG_ACTIVE_CELLS]   Secondary positions: {sorted(secondary_positions)}")
                else:
                    logger.debug(f"🔍 [DEBUG_ACTIVE_CELLS]   Secondary positions: None (1x1 camera)")

        except Exception as e:
            logger.error(f"Error in _debug_log_active_cells_state: {e}")

    @Slot(int, result='QVariantMap')
    def getCellDimensions(self, position):
        """Get the dimensions of a cell in terms of grid cells

        Args:
            position: The position of the cell in the grid

        Returns:
            Dictionary with width and height in grid cells, or default values if not set
        """
        if position in self._cell_dimensions:
            return self._cell_dimensions[position]
        return {"width": 1, "height": 1}  # Default dimensions

    def _convert_qjs_value_to_python(self, qjs_value):
        """Convert QJSValue to Python object"""
        try:
            from PySide6.QtQml import QJSValue

            if isinstance(qjs_value, QJSValue):
                if qjs_value.isString():
                    return qjs_value.toString()
                elif qjs_value.isNumber():
                    return qjs_value.toNumber()
                elif qjs_value.isBool():
                    return qjs_value.toBool()
                elif qjs_value.isArray():
                    # Convert array to list
                    result = []
                    length = qjs_value.property("length").toInt()
                    for i in range(length):
                        item = qjs_value.property(i)
                        result.append(self._convert_qjs_value_to_python(item))
                    return result
                elif qjs_value.isObject():
                    # Convert object to dict - use toVariant() for objects
                    variant = qjs_value.toVariant()
                    if isinstance(variant, dict):
                        # Recursively convert nested QJSValues
                        result = {}
                        for key, value in variant.items():
                            result[key] = self._convert_qjs_value_to_python(value)
                        return result
                    else:
                        return variant
                elif qjs_value.isNull() or qjs_value.isUndefined():
                    return None
                else:
                    # Try to convert to variant and then to Python
                    return qjs_value.toVariant()
            else:
                # Already a Python object
                return qjs_value
        except Exception as e:
            logger.error(f"Error converting QJSValue to Python: {e}")
            # Fallback: try toVariant() directly
            try:
                if hasattr(qjs_value, 'toVariant'):
                    return qjs_value.toVariant()
                else:
                    return qjs_value
            except:
                return qjs_value

    @Slot(int, 'QVariantMap')
    def addGroup(self, position, group_id):
        """Add all cameras from a group to the grid, starting from the specified position

        Args:
            position: The starting position in the grid to add the group's cameras
            group_data: Dictionary containing group information including:
                - id: Group ID
                - data: Dictionary containing group details
                    - name: Group name
                    - server_ip: Server IP address
                    - cameraIds: List of camera IDs in the group
        """
        if not group_id:
            self.actionCompleted.emit("addGroup", False, "Không tìm thấy group ID trong dữ liệu truyền vào")
            return None

        # Get group model
        group_model = group_model_manager.get_group_model(id=group_id)
        logger.debug(f"[DEBUG_ADD_GROUP] group_model: {group_model}")
        if not group_model:
            logger.warning(f"[DEBUG_ADD_GROUP] Group model not found for id: {group_id}")
            self.actionCompleted.emit("addGroup", False, f"Không tìm thấy group model với id: {group_id}")
            return None

        # Get camera IDs from group model
        camera_ids = group_model.get_property("cameraIds", [])
        logger.debug(f"[DEBUG_ADD_GROUP] camera_ids: {camera_ids}")
        logger.debug(f"[DEBUG_ADD_GROUP] Group contains {len(camera_ids)} cameras")

        # Get list of cameras in the group
        list_camera = []
        for camera_id in camera_ids:
            camera_model = camera_model_manager.get_camera_model(id=camera_id)
            logger.debug(f"[DEBUG_ADD_GROUP] camera_id: {camera_id}, camera_model: {camera_model}")
            if camera_model and hasattr(camera_model, 'data') and camera_model.data:
                list_camera.append(camera_model)
            else:
                logger.warning(f"[DEBUG_ADD_GROUP] Invalid camera model for ID: {camera_id}")

        logger.debug(f"[DEBUG_ADD_GROUP] Số camera hợp lệ: {len(list_camera)}")

        if not list_camera:
            logger.warning(f"[DEBUG_ADD_GROUP] No valid cameras found in group: {group_id}")
            self.actionCompleted.emit("addGroup", False, f"Không có camera hợp lệ trong group: {group_id}")
            return None

        # Find all available positions in order
        max_pos = max(self._active_cells.keys()) if self._active_cells else -1
        all_positions = set(range(max_pos + 2))
        available_positions = sorted(list(all_positions - set(self._active_cells.keys())))
        logger.debug(f"[DEBUG_ADD_GROUP] available_positions: {available_positions}")

        if not available_positions:
            logger.warning("[DEBUG_ADD_GROUP] No available positions")
            self.actionCompleted.emit("addGroup", False, "Không còn vị trí trống để thêm camera")
            return None

        # Use first available position if none specified
        start_position = position if position is not None else available_positions[0]
        added_positions = []

        try:
            # Kiểm tra xem grid có đang ở chế độ fullscreen không
            was_fullscreen = self._is_maximized

            # Nếu đang ở chế độ fullscreen, tắt chế độ fullscreen trước khi thêm camera mới
            if was_fullscreen:
                # Sử dụng phương thức restoreGrid có sẵn để đảm bảo tính nhất quán
                self.restoreGrid()

            # Begin insert rows for all cameras at once
            self.beginInsertRows(QModelIndex(), len(self._active_cells),
                               len(self._active_cells) + len(list_camera) - 1)

            # Add all cameras
            # Xử lý khác nhau tùy thuộc vào autoResizeEnabled
            if self._auto_resize_enabled:
                # Tìm vị trí trống cho tất cả camera, bắt đầu từ start_position
                available_positions = []
                current_position = start_position

                # Tìm đủ số vị trí trống cho tất cả camera trong nhóm
                while len(available_positions) < len(list_camera):
                    # Nếu vị trí hiện tại không có trong active_cells hoặc giá trị là None, thêm vào danh sách vị trí trống
                    if current_position not in self._active_cells or self._active_cells[current_position] is None:
                        available_positions.append(current_position)

                    current_position += 1
            else:
                # Sử dụng logic cũ: các vị trí liên tiếp từ start_position
                available_positions = [start_position + i for i in range(len(list_camera))]

            for i, camera_model in enumerate(list_camera):
                try:
                    camera_position = available_positions[i]

                    # Check camera model validity
                    if not hasattr(camera_model, 'data') or not camera_model.data:
                        logger.warning(f"[DEBUG_ADD_GROUP] Invalid camera model at index {i}")
                        continue

                    if not camera_model.get_property("id", None):
                        logger.warning(f"[DEBUG_ADD_GROUP] Camera model has no ID at index {i}")
                        continue

                    # Add camera model to active cells
                    self._active_cells[camera_position] = camera_model
                    added_positions.append(camera_position)

                    # Luôn cập nhật _cell_dimensions, ngay cả khi kích thước là 1x1
                    self._cell_dimensions[camera_position] = {
                        "width": 1,
                        "height": 1
                    }

                except Exception as camera_error:
                    logger.error(f"❌ [DEBUG_ADD_GROUP] Error adding camera {i}: {camera_error}")
                    logger.error(f"❌ [DEBUG_ADD_GROUP] Camera error traceback: {traceback.format_exc()}")
                    continue

            # End insert rows operation
            logger.debug(f"[DEBUG_ADD_GROUP] Ending insert rows operation")
            self.endInsertRows()
            logger.debug(f"[DEBUG_ADD_GROUP] endInsertRows completed successfully")

        except Exception as e:
            logger.error(f"❌ [DEBUG_ADD_GROUP] Error during batch camera addition: {e}")
            logger.error(f"❌ [DEBUG_ADD_GROUP] Batch addition error traceback: {traceback.format_exc()}")
            # Ensure we end the insert rows operation even if an error occurred
            try:
                logger.debug(f"[DEBUG_ADD_GROUP] Attempting to end insert rows after error")
                self.endInsertRows()
                logger.debug(f"[DEBUG_ADD_GROUP] Successfully ended insert rows after error")
            except Exception as end_rows_error:
                logger.error(f"❌ [DEBUG_ADD_GROUP] Error ending insert rows: {end_rows_error}")
            self.actionCompleted.emit("addGroup", False, f"Lỗi khi thêm group: {e}")
            return None

        # Update grid if cameras were added successfully
        if added_positions:
            logger.debug(f"[DEBUG_ADD_GROUP] Updating grid with {len(added_positions)} added cameras")

            # Update layout
            logger.debug(f"[DEBUG_ADD_GROUP] Updating layout")
            self._layout = sorted(list(self._active_cells.keys()))
            self.layoutChanged.emit(self._layout)
            logger.debug(f"[DEBUG_ADD_GROUP] Layout updated successfully")

            # Update grid size if needed
            if self._auto_resize_enabled:
                logger.debug(f"🔄 [DEBUG_ADD_GROUP] Calculating new grid size")
                new_cols, new_rows = self._calculateGridSize(len(self._active_cells))
                logger.debug(f"🔄 [DEBUG_ADD_GROUP] New grid size: {new_cols}x{new_rows}, Current: {self._columns}x{self._rows}")

                grid_changed = False
                if new_cols != self._columns or new_rows != self._rows:
                    logger.debug(f"🔄 [DEBUG_ADD_GROUP] Updating grid dimensions")
                    self._columns = new_cols
                    self._rows = new_rows
                    self.columnsChanged.emit(self._columns)
                    self.rowsChanged.emit(self._rows)
                    grid_changed = True
                    logger.debug(f"🔄 [DEBUG_ADD_GROUP] Grid dimensions updated successfully")

                # If grid size changed, emit cellDimensionsChanged for all existing cameras
                # so they can update their visual size based on new grid dimensions
                if grid_changed:
                    for pos, dimensions in self._cell_dimensions.items():
                        if pos in self._active_cells:  # Only for active cameras
                            self.cellDimensionsChanged.emit(pos, dimensions["width"], dimensions["height"])

            # Emit signals for all added cameras
            logger.debug(f"[DEBUG_ADD_GROUP] Emitting videoInfoChanged signals for {len(added_positions)} cameras")
            for i, position in enumerate(added_positions):
                camera_model = self._active_cells[position]
                camera_id = camera_model.get_property('id')
                logger.debug(f"[DEBUG_ADD_GROUP] Emitting videoInfoChanged for camera {i+1}/{len(added_positions)} at position {position}")
                # Check if the camera supports PTZ
                supports_ptz = False
                if camera_model:
                    ptz_caps = camera_model.get_property("ptzCap", [])
                    supports_ptz = ptz_caps is not None and len(ptz_caps) > 0
                self.videoInfoChanged.emit(position, camera_model, True, supports_ptz)

            logger.debug(f"[DEBUG_ADD_GROUP] All videoInfoChanged signals emitted successfully")

            # Không gọi start_processing() ở đây nữa
            # Thay vào đó, chúng ta sẽ đợi tất cả các item được tạo xong thông qua Component.onCompleted trong CustomVideo.qml
            logger.debug(f"✅ [DEBUG_ADD_GROUP] Successfully added {len(added_positions)} cameras")
            logger.debug(f"[DEBUG_ADD_GROUP] ===== GROUP ADDITION COMPLETED =====")
            return start_position

        logger.debug(f"❌ [DEBUG_ADD_GROUP] No cameras were added")
        self.actionCompleted.emit("addGroup", False, "Không có camera nào được thêm vào lưới")
        return None

    @Slot(int, str)
    def itemRegisteredVideocapture(self, position, camera_id):
        """Slot để nhận thông báo khi một item đã đăng ký video capture (isPlaying = True)"""
        try:
            logger.debug(f"🔄 [DEBUG_ITEM_READY] Item registered video capture at position {position}, camera_id: {camera_id}")

            # Kiểm tra position có hợp lệ không
            if position < 0:
                logger.warning(f"❌ [DEBUG_ITEM_READY] Invalid position: {position}")
                return

            # Kiểm tra camera_id có hợp lệ không
            if not camera_id:
                logger.warning(f"❌ [DEBUG_ITEM_READY] Invalid camera_id for position {position}")
                return

            # Kiểm tra position có trong active_cells không
            if position not in self._active_cells:
                logger.warning(f"❌ [DEBUG_ITEM_READY] Position {position} not in active cells")
                return

            # Kiểm tra camera_id có khớp với active_cells không
            camera_model = self._active_cells[position]
            if camera_model.get_property('id') != camera_id:
                logger.warning(f"❌ [DEBUG_ITEM_READY] Camera ID mismatch at position {position}: expected {camera_model.get_property('id')}, got {camera_id}")
                return

            # Lưu thông tin item đã sẵn sàng
            self._ready_items[position] = camera_id
            logger.debug(f"🔄 [DEBUG_ITEM_READY] Added position {position} to ready items, total ready: {len(self._ready_items)}")

            # Kiểm tra xem tất cả các item đã sẵn sàng chưa
            logger.debug(f"🔄 [DEBUG_ITEM_READY] Checking if all items are ready")
            self._check_all_items_ready()

        except Exception as e:
            logger.error(f"❌ [DEBUG_ITEM_READY] Error in itemRegisteredVideocapture: {e}")
            logger.error(f"❌ [DEBUG_ITEM_READY] Exception traceback: {traceback.format_exc()}")

    def _check_all_items_ready(self):
        """Kiểm tra xem tất cả các item đã sẵn sàng chưa"""
        try:
            logger.debug(f"🔄 [DEBUG_CHECK_READY] Starting check for all items ready")

            # Lấy danh sách các vị trí đang hoạt động
            active_positions = self.getActivePositions()
            logger.debug(f"🔄 [DEBUG_CHECK_READY] Found {len(active_positions)} active positions")

            # Nếu không có vị trí nào đang hoạt động, không cần kiểm tra
            if not active_positions:
                logger.debug(f"🔄 [DEBUG_CHECK_READY] No active positions, skipping check")
                return

            # Kiểm tra xem tất cả các vị trí đang hoạt động đã sẵn sàng chưa
            all_ready = True
            not_ready_count = 0
            for pos in active_positions:
                if pos not in self._ready_items:
                    all_ready = False
                    not_ready_count += 1

            if not_ready_count > 0:
                logger.debug(f"🔄 [DEBUG_CHECK_READY] {not_ready_count}/{len(active_positions)} items are not ready yet")

            if all_ready:
                logger.debug(f"✅ [DEBUG_CHECK_READY] All {len(active_positions)} items ready, starting video processing")

                # Tất cả các item đã sẵn sàng, gọi start_processing
                try:
                    logger.debug(f"🔄 [DEBUG_CHECK_READY] Calling video_capture_controller.start_processing()")
                    video_capture_controller.start_processing()
                    logger.debug(f"✅ [DEBUG_CHECK_READY] start_processing() called successfully")
                except Exception as vc_error:
                    logger.error(f"❌ [DEBUG_CHECK_READY] Error calling start_processing: {vc_error}")
                    logger.error(f"❌ [DEBUG_CHECK_READY] Exception traceback: {traceback.format_exc()}")

                # Emit signal để thông báo tất cả các item đã sẵn sàng
                try:
                    logger.debug(f"🔄 [DEBUG_CHECK_READY] Emitting allItemsReady signal")
                    self.allItemsReady.emit()
                    logger.debug(f"✅ [DEBUG_CHECK_READY] allItemsReady signal emitted successfully")
                except Exception as signal_error:
                    logger.error(f"❌ [DEBUG_CHECK_READY] Error emitting allItemsReady signal: {signal_error}")
                    logger.error(f"❌ [DEBUG_CHECK_READY] Exception traceback: {traceback.format_exc()}")

        except Exception as e:
            logger.error(f"❌ [DEBUG_CHECK_READY] Error in _check_all_items_ready: {e}")
            logger.error(f"❌ [DEBUG_CHECK_READY] Exception traceback: {traceback.format_exc()}")

    @Slot(int, 'QVariantMap')
    def addMap(self, position, map_data):
        """Add a map to the grid at the specified position"""
        logger.debug(f"🔄 Starting map addition process for position: {position}")
        logger.debug(f"- Map data: {map_data}")
        # Find all available positions in order
        max_pos = max(self._active_cells.keys()) if self._active_cells else -1
        all_positions = set(range(max_pos + 2))  # +2 to have one more position after max
        available_positions = sorted(list(all_positions - set(self._active_cells.keys())))

        if not available_positions:
            logger.debug("❌ No available positions to add a camera.")
            return None

        # Nếu không có vị trí được chỉ định, sử dụng vị trí đầu tiên có sẵn
        if position is None:
            position = available_positions[0]
        # Nếu vị trí đã bị chiếm
        elif position in self._active_cells:
            # Nếu auto_resize_enabled là True, sử dụng vị trí trống tiếp theo
            if self._auto_resize_enabled:
                logger.debug(f"Position {position} is already occupied. Auto-resize is enabled, using next available position.")
                position = available_positions[0]
            # Nếu auto_resize_enabled là False, vẫn sử dụng vị trí đó (thay thế camera cũ)
            else:
                logger.debug(f"Position {position} is already occupied. Auto-resize is disabled, will replace existing camera.")
        elif position in self._active_cells.values():
            # Nếu auto_resize_enabled là True, sử dụng vị trí trống tiếp theo
            if self._auto_resize_enabled:
                logger.debug(f"Value {position} is already in use. Auto-resize is enabled, using next available position.")
                position = available_positions[0]
            # Nếu auto_resize_enabled là False, vẫn sử dụng vị trí đó (thay thế camera cũ)
            else:
                logger.debug(f"Value {position} is already in use. Auto-resize is disabled, will replace existing camera.")

        try:
            # Kiểm tra xem grid có đang ở chế độ fullscreen không
            was_fullscreen = self._is_maximized

            # Nếu đang ở chế độ fullscreen, tắt chế độ fullscreen trước khi thêm camera mới
            if was_fullscreen:
                logger.debug(f"🔄 [DEBUG_ADD_MAP] Grid is in fullscreen mode, restoring to normal size before adding camera")
                # Sử dụng phương thức restoreGrid có sẵn để đảm bảo tính nhất quán
                self.restoreGrid()
                logger.debug(f"🔄 [DEBUG_ADD_MAP] Grid restored to normal size: {self._columns}x{self._rows}")

            # Begin insert row
            self.beginInsertRows(QModelIndex(), len(self._active_cells), len(self._active_cells))

            # Add camera to active cells
            self._active_cells[position] = map_data["id"]

            # Update grid size if auto-resize is enabled
            if self._auto_resize_enabled:
                new_count = max(self._active_cells.keys(), default=0) + 1
                new_cols, new_rows = self._calculateGridSize(new_count)

                if new_cols != self._columns:
                    self._columns = new_cols
                    self.columnsChanged.emit(self._columns)
                if new_rows != self._rows:
                    self._rows = new_rows
                    self.rowsChanged.emit(self._rows)

            # Update next_position
            max_pos = max(self._active_cells.keys())
            all_positions = set(range(max_pos + 2))
            available_positions = sorted(list(all_positions - set(self._active_cells.keys())))
            self._next_position = available_positions[0] if available_positions else max_pos + 1

            # End insert row operation
            self.endInsertRows()
            # logger.debug(f'map_data = {map_data}')
            modelType = map_data.get("type",None)
            logger.debug(f'modelType = {modelType}')
            if modelType == TreeType.List_Map:
                model = map_manager.get_map_model(id=map_data.get("id",None))
            elif modelType == TreeType.FloorItem:
                model = floor_manager.get_floor(id=map_data.get("id",None))
            else:
                model = None
            if model is not None:
                self.mapInfoChanged.emit(position, model)

            # Không gọi start_processing() ở đây nữa
            # Thay vào đó, chúng ta sẽ đợi itemReady được gọi

            logger.debug(f"✅ Successfully added map at position {position}")
            return position

        except Exception as e:
            logger.error(f"❌ Error during map addition: {e}")
            # Ensure we end the insert row operation if an error occurred
            try:
                self.endInsertRows()
            except:
                pass
            return None

    @Slot(str)
    def addGroupToPositions(self, group_info_json):
        """Add cameras from a group to specific positions in the grid

        Args:
            group_info_json: JSON string containing:
                - groupData: Dictionary with group information
                - positions: List of positions to add cameras to
        """
        try:
            import json

            # Parse the JSON string
            group_info = json.loads(group_info_json)

            # Handle both old format (groupData) and new format (direct id)
            if 'groupData' in group_info:
                group_data = group_info.get('groupData', {})
            else:
                # New format: direct id and positions
                group_data = {'id': group_info.get('id')}

            positions = group_info.get('positions', [])

            logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] ===== STARTING GROUP ADDITION TO POSITIONS =====")
            logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Group data ID: {group_data.get('id', 'Unknown')}")
            logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Positions: {positions}")

            # Get group model
            logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Getting group model for ID: {group_data['id']}")
            group_model = group_model_manager.get_group_model(id=group_data["id"])
            if not group_model:
                logger.warning(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] Group model not found for id: {group_data['id']}")
                return None
            logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Successfully got group model")

            # Get camera IDs from group model
            camera_ids = group_model.get_property("cameraIds") or []
            logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Getting camera models for {len(camera_ids)} cameras")
            list_camera = []
            for i, camera_id in enumerate(camera_ids):
                logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Getting camera model {i+1}/{len(camera_ids)}: {camera_id}")
                camera_model = camera_model_manager.get_camera_model(id=camera_id)
                if camera_model and hasattr(camera_model, 'data') and camera_model.data:
                    list_camera.append(camera_model)
                    logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Successfully got camera model for {camera_id}")
                else:
                    logger.warning(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] Invalid camera model for ID: {camera_id}")

            if not list_camera:
                logger.warning(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] No valid cameras found in group: {group_data['id']}")
                return None

            logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Successfully got {len(list_camera)} valid camera models")

            # Check if we have enough positions
            if len(positions) < len(list_camera):
                logger.warning(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] Not enough positions ({len(positions)}) for cameras ({len(list_camera)})")
                return None

            try:
                # Kiểm tra xem grid có đang ở chế độ fullscreen không
                was_fullscreen = self._is_maximized

                # Nếu đang ở chế độ fullscreen, tắt chế độ fullscreen trước khi thêm camera mới
                if was_fullscreen:
                    logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Grid is in fullscreen mode, restoring to normal size before adding group")
                    # Sử dụng phương thức restoreGrid có sẵn để đảm bảo tính nhất quán
                    self.restoreGrid()
                    logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Grid restored to normal size: {self._columns}x{self._rows}")

                # Begin insert rows for all cameras at once
                logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Beginning insert rows operation")
                logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Current active cells: {len(self._active_cells)}")
                logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Will add {len(list_camera)} cameras")

                self.beginInsertRows(QModelIndex(), len(self._active_cells),
                                   len(self._active_cells) + len(list_camera) - 1)
                logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] beginInsertRows completed successfully")

                # Add cameras to specified positions
                logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Adding {len(list_camera)} cameras to grid")
                added_positions = []

                # Xử lý khác nhau tùy thuộc vào autoResizeEnabled
                if self._auto_resize_enabled:
                    logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Auto resize is enabled, finding non-overlapping positions")

                    # Kiểm tra và điều chỉnh vị trí nếu cần
                    available_positions = []
                    for i, pos in enumerate(positions):
                        if i >= len(list_camera):
                            break

                        camera_position = pos

                        # Kiểm tra xem vị trí đã có camera chưa
                        while camera_position in self._active_cells and self._active_cells[camera_position] is not None:
                            logger.warning(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] Position {camera_position} is already occupied")
                            camera_position += 1

                        available_positions.append(camera_position)

                    logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Adjusted positions with auto resize: {available_positions}")
                else:
                    logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Auto resize is disabled, using original positions")

                    # Sử dụng logic cũ: giữ nguyên các vị trí ban đầu
                    available_positions = positions[:len(list_camera)]
                    logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Using original positions: {available_positions}")

                for i, camera_model in enumerate(list_camera):
                    if i >= len(available_positions):
                        logger.warning(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] No position available for camera {i+1}")
                        break

                    try:
                        camera_position = available_positions[i]
                        logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Adding camera {i+1}/{len(list_camera)} at position {camera_position}")

                        # Check camera model validity
                        if not hasattr(camera_model, 'data') or not camera_model.data:
                            logger.warning(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] Invalid camera model at index {i}")
                            continue

                        if not camera_model.get_property("id", None):
                            logger.warning(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] Camera model has no ID at index {i}")
                            continue

                        # Add camera model to active cells
                        self._active_cells[camera_position] = camera_model
                        added_positions.append(camera_position)
                        logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Added camera {camera_model.get_property('id')} at position {camera_position}")

                        # Luôn cập nhật _cell_dimensions, ngay cả khi kích thước là 1x1
                        self._cell_dimensions[camera_position] = {
                            "width": 1,
                            "height": 1
                        }
                        logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Set cell dimensions for position {camera_position}: 1x1")

                        # Update tab model if available
                        # if self._tab_model:
                        #     logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Updating tab model for camera at position {camera_position}")
                        #     grid_item = self._tab_model.listGridData.get(camera_position, None)

                        #     # Sau khi gọi restoreGrid(), grid không còn ở chế độ fullscreen nữa
                        #     # Nhưng vẫn kiểm tra để đảm bảo tính nhất quán
                        #     is_fullscreen_mode = self._is_maximized
                        #     logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Grid is in fullscreen mode: {is_fullscreen_mode}")

                        #     if grid_item:
                        #         # Cập nhật grid_item hiện có
                        #         grid_item.type = CommonEnum.ItemType.CAMERA
                        #         grid_item.row = camera_position // self._columns
                        #         grid_item.col = camera_position % self._columns
                        #         grid_item.width = 1
                        #         grid_item.height = 1

                        #         # Kiểm tra này chỉ để đảm bảo tính nhất quán, nhưng sau khi gọi restoreGrid()
                        #         # is_fullscreen_mode sẽ luôn là False và _active_item_position sẽ luôn là None
                        #         if is_fullscreen_mode and self._active_item_position is not None:
                        #             logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Grid is in fullscreen mode, using active position: {self._active_item_position}")
                        #             grid_item.row = self._active_item_position // self._columns
                        #             grid_item.col = self._active_item_position % self._columns

                        #         self._tab_model.set_model(grid_item=grid_item, model=camera_model)
                        #         self._tab_model.add_grid_item_signal.emit(grid_item.index)
                        #         logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Tab model updated for camera at position {camera_position}")
                        #     else:
                        #         # Tạo mới grid_item nếu không tồn tại
                        #         from src.common.model.tab_model import GridItem, ItemType

                        #         # Tính toán row và col
                        #         row = camera_position // self._columns
                        #         col = camera_position % self._columns

                        #         # Kiểm tra này chỉ để đảm bảo tính nhất quán, nhưng sau khi gọi restoreGrid()
                        #         # is_fullscreen_mode sẽ luôn là False và _active_item_position sẽ luôn là None
                        #         if is_fullscreen_mode and self._active_item_position is not None:
                        #             logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Grid is in fullscreen mode, using active position: {self._active_item_position}")
                        #             row = self._active_item_position // self._columns
                        #             col = self._active_item_position % self._columns

                        #         # Tạo mới grid_item
                        #         grid_item = GridItem(
                        #             index=camera_position,
                        #             type=CommonEnum.ItemType.CAMERA,
                        #             row=row,
                        #             col=col,
                        #             width=1,
                        #             height=1,
                        #             is_fullscreen=is_fullscreen_mode,
                        #             is_virtual_fullscreen=False,
                        #             model=camera_model
                        #         )

                        #         # Thêm grid_item vào tab_model
                        #         self._tab_model.add_grid_item(item=grid_item)
                        #         self._tab_model.add_grid_item_signal.emit(grid_item.index)
                        #         logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Created new grid item for camera at position {camera_position}")

                        #     # Cập nhật tab_model direction
                        #     if self._tab_model.data and self._tab_model.data.type != TabType.Invalid:
                        #         logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Updating tab model direction")
                        #         controller = controller_manager.get_controller(
                        #             server_ip=group_data["data"]["server_ip"])
                        #         if controller:
                        #             self._tab_model.direction = {
                        #                 'id': str(uuid.uuid4()),
                        #                 'type': SignalType.DropGroup,
                        #                 'data': {
                        #                     'key': grid_item.index,
                        #                     'camera_id': camera_model.get_property('id'),
                        #                     'group_id': group_data["id"],
                        #                     'server_ip': group_data["data"]["server_ip"],
                        #                     'row': grid_item.row,
                        #                     'col': grid_item.col
                        #                 }
                        #             }
                        #             controller.update_tabmodel_by_put(parent=self, tab=self._tab_model)
                        #             logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Tab model direction updated")

                    except Exception as camera_error:
                        logger.error(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] Error adding camera {i}: {camera_error}")
                        logger.error(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] Camera error traceback: {traceback.format_exc()}")
                        continue

                # End insert rows operation
                logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Ending insert rows operation")
                self.endInsertRows()
                logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] endInsertRows completed successfully")

            except Exception as e:
                logger.error(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] Error during batch camera addition: {e}")
                logger.error(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] Batch addition error traceback: {traceback.format_exc()}")
                # Ensure we end the insert rows operation even if an error occurred
                try:
                    logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Attempting to end insert rows after error")
                    self.endInsertRows()
                    logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Successfully ended insert rows after error")
                except Exception as end_rows_error:
                    logger.error(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] Error ending insert rows: {end_rows_error}")
                self.actionCompleted.emit("addGroup", False, f"Lỗi khi thêm group: {e}")
                return None

            # Update grid if cameras were added successfully
            if added_positions:
                logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Updating grid with {len(added_positions)} added cameras")

                # Update layout
                logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Updating layout")
                self._layout = sorted(list(self._active_cells.keys()))
                self.layoutChanged.emit(self._layout)
                logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Layout updated successfully")

                # Update grid size if needed
                if self._auto_resize_enabled:
                    logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Calculating new grid size")
                    new_cols, new_rows = self._calculateGridSize(len(self._active_cells))
                    logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] New grid size: {new_cols}x{new_rows}, Current: {self._columns}x{self._rows}")

                    grid_changed = False
                    if new_cols != self._columns or new_rows != self._rows:
                        logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Updating grid dimensions")
                        self._columns = new_cols
                        self._rows = new_rows
                        self.columnsChanged.emit(self._columns)
                        self.rowsChanged.emit(self._rows)
                        grid_changed = True
                        logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Grid dimensions updated successfully")

                    # If grid size changed, emit cellDimensionsChanged for all existing cameras
                    # so they can update their visual size based on new grid dimensions
                    if grid_changed:
                        for pos, dimensions in self._cell_dimensions.items():
                            if pos in self._active_cells:  # Only for active cameras
                                self.cellDimensionsChanged.emit(pos, dimensions["width"], dimensions["height"])

                # Emit signals for all added cameras
                logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Emitting videoInfoChanged signals for {len(added_positions)} cameras")
                for i, position in enumerate(added_positions):
                    camera_model = self._active_cells[position]
                    camera_id = camera_model.get_property('id')
                    logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] Emitting videoInfoChanged for camera {i+1}/{len(added_positions)} at position {position}")
                    supports_ptz = False
                    if camera_model:
                        ptz_caps = camera_model.get_property("ptzCap", [])
                        supports_ptz = ptz_caps is not None and len(ptz_caps) > 0
                    self.videoInfoChanged.emit(position, camera_model, True, supports_ptz)

                logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] All videoInfoChanged signals emitted successfully")

                # Không gọi start_processing() ở đây nữa
                # Thay vào đó, chúng ta sẽ đợi tất cả các item được tạo xong thông qua Component.onCompleted trong CustomVideo.qml
                logger.debug(f"✅ [DEBUG_ADD_GROUP_TO_POSITIONS] Successfully added {len(added_positions)} cameras")
                logger.debug(f"🔄 [DEBUG_ADD_GROUP_TO_POSITIONS] ===== GROUP ADDITION TO POSITIONS COMPLETED =====")
                return added_positions[0] if added_positions else None

            logger.debug(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] No cameras were added")
            self.actionCompleted.emit("addGroup", False, "Không có camera nào được thêm vào lưới")
            return None

        except Exception as e:
            logger.error(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] Error in addGroupToPositions: {e}")
            logger.error(f"❌ [DEBUG_ADD_GROUP_TO_POSITIONS] Exception traceback: {traceback.format_exc()}")
            return None

    @Slot(int, str)
    def addEvent(self, position, event_name):
        """Add an event to the grid at the specified position"""

        # Xử lý kịch bản event sau
        # if not self._tab_model:
        #     logger.warning("No tab model set")
        #     return

        # event_model = event_manager.get_event(event_name)
        # if not event_model:
        #     logger.warning(f"Event model not found for name: {event_name}")
        #     return

        # grid_item = self._tab_model.listGridData.get(position, None)
        # if not grid_item:
        #     logger.warning(f"No grid item at position: {position}")
        #     return

        # if grid_item.model == event_model:
        #     logger.debug("Event already exists at this position")
        #     return

        # grid_item.type = CommonEnum.ItemType.EVENT
        # grid_item.row = position // self._columns
        # grid_item.col = position % self._columns
        # grid_item.width = 1
        # grid_item.height = 1

        # self._tab_model.set_model(grid_item=grid_item, model=event_model)
        # self._tab_model.add_grid_item_signal.emit(grid_item.index)

    @Property(bool, notify=isDarkThemeChanged)
    def isDarkTheme(self):
        return self._is_dark_theme

    @isDarkTheme.setter
    def isDarkTheme(self, value):
        if self._is_dark_theme != value:
            self._is_dark_theme = value
            self.isDarkThemeChanged.emit()

    @Property(int, notify=cameraCountChanged)
    def cameraCount(self):
        """Get the current number of cameras in the grid"""
        return self._camera_count

    # Context menu methods for opening cameras in different views/tabs
    @Slot(int)
    def openCameraInNewView(self, position):
        """Create a new tab model and add camera to it using share_frame_signal"""
        try:
            if position not in self._active_cells:
                logger.warning(f"No camera found at position {position}")
                return

            camera_model = self._active_cells[position]
            if not camera_model:
                logger.warning(f"No valid camera model found for position {position}")
                return

            # Create new tab model (Invalid type = regular tab)
            from src.common.model.tab_model import TabType
            from src.presentation.camera_screen.managers.grid_manager import gridManager
            import uuid

            # Generate unique name for new tab using proper naming logic
            camera_screen = main_controller.list_parent['CameraScreen']
            tab_name = camera_screen.find_name_selected(tab_type=TabType.Invalid)

            # Create new tab model data
            data = {
                "name": tab_name,
                "type": TabType.Invalid,  # Regular tab
                "isShow": True,
                "currentGrid": "{}",
                "listGridData": "{}",
                "listGridCustomData": "NewGrid",
                "direction": str({'id': str(uuid.uuid4())})
            }

            # Create GridModel and add to manager
            new_grid_model = GridModel(data=data)
            gridManager.addGridModel(data=new_grid_model)

            # Add tab widget to UI first
            camera_screen = main_controller.list_parent['CameraScreen']
            camera_screen.add_tab_widget(gridModel=new_grid_model)

            # Add camera to the new grid at position 0 using addCamera method
            camera_data = {
                "id": camera_model.get_property('id')
            }
            new_grid_model.addCamera(0, camera_data)

            logger.debug(f"Created new view '{tab_name}' with camera from position {position}")

        except Exception as e:
            logger.error(f"Error opening camera in new view: {e}")

    @Slot(int)
    def openCameraInNewSavedView(self, position):
        """Create a new saved view through tree view and add camera to it"""
        try:
            if position not in self._active_cells:
                logger.warning(f"No camera found at position {position}")
                return

            camera_model = self._active_cells[position]
            if not camera_model:
                logger.warning(f"No valid camera model found for position {position}")
                return

            # Get tree view to create saved view properly
            main_tree_view = main_controller.list_parent['MainTreeView']

            # Store camera info for later use
            camera_id = camera_model.get_property('id')

            # Find saved view list item and trigger add_saved_view_triggered
            from src.common.model.main_tree_view_model import TreeType

            # Find saved view list item
            saved_view_list_item = main_tree_view.get_item('Saved View List', tree_type=TreeType.List_Saved_View)
            if saved_view_list_item:
                # Store camera info for adding after saved view is created
                self._pending_camera_for_saved_view = camera_id

                # Trigger add saved view action (this will create saved view properly)
                main_tree_view.add_saved_view_triggered(saved_view_list_item)

                logger.debug(f"Initiated new saved view creation with camera from position {position}")
            else:
                logger.warning("Could not find Saved View List item in tree view")

        except Exception as e:
            logger.error(f"Error opening camera in new saved view: {e}")

    @Slot(int)
    def openCameraInNewVirtualWindow(self, position):
        """Create a new virtual window through tree view and add camera to it"""
        try:
            if position not in self._active_cells:
                logger.warning(f"No camera found at position {position}")
                return

            camera_model = self._active_cells[position]
            if not camera_model:
                logger.warning(f"No valid camera model found for position {position}")
                return

            # Get tree view to create virtual window properly
            main_tree_view = main_controller.list_parent['MainTreeView']

            # Store camera info for later use
            camera_id = camera_model.get_property('id')

            # Find virtual window list item and trigger new_virtual_window_triggered
            from src.common.model.main_tree_view_model import TreeType

            # Find virtual window list item
            virtual_window_list_item = main_tree_view.get_item('Virtual Window List', tree_type=TreeType.List_Virtual_Window)
            if virtual_window_list_item:
                # Store camera info for adding after virtual window is created
                self._pending_camera_for_virtual_window = camera_id

                # Trigger new virtual window creation (this will open dialog)
                main_tree_view.new_virtual_window_triggered(virtual_window_list_item)

                logger.debug(f"Initiated new virtual window creation with camera from position {position}")
            else:
                logger.warning("Could not find Virtual Window List item in tree view")

        except Exception as e:
            logger.error(f"Error opening camera in new virtual window: {e}")

    @Slot(int, str, int, int)
    def openCameraInExistingTab(self, position, tabName, row, col):
        """Add camera to an existing saved view or virtual window tab at specified grid position"""
        try:
            if position not in self._active_cells:
                logger.warning(f"No camera found at position {position}")
                return

            camera_model = self._active_cells[position]
            if not camera_model:
                logger.warning(f"No valid camera model found for position {position}")
                return

            # Find the existing tab (saved view or virtual window)
            from src.common.model.tab_model import tab_model_manager, TabType
            target_tab_model = None
            target_grid_model = None

            # Find the tab model by name (support both SavedView and VirtualWindow)
            for _, tab_model in tab_model_manager.tab_model_list.items():
                tab_name = tab_model.get_property('name')
                tab_type = None

                # Safe access to tab type
                if hasattr(tab_model, 'data'):
                    if isinstance(tab_model.data, dict):
                        tab_type = tab_model.data.get('type')
                    else:
                        tab_type = getattr(tab_model.data, 'type', None)

                if (tab_name == tabName and
                    tab_type in [TabType.SavedView, TabType.VirtualWindow]):
                    target_tab_model = tab_model
                    break

            if not target_tab_model:
                logger.warning(f"Tab '{tabName}' not found")
                return

            # Find the corresponding grid model
            from src.presentation.camera_screen.managers.grid_manager import gridManager
            for _, grid_model in gridManager.data.items():
                if grid_model.get_property('name') == tabName:
                    target_grid_model = grid_model
                    break

            if not target_grid_model:
                logger.warning(f"Grid model for tab '{tabName}' not found")
                return

            # Calculate grid position from row, col
            grid_position = row * target_grid_model.columns + col

            # Add camera to the target grid using addCamera method
            camera_data = {
                "id": camera_model.get_property('id')
            }
            target_grid_model.addCamera(grid_position, camera_data)

            logger.debug(f"Added camera from position {position} to tab '{tabName}' at grid position ({row}, {col})")

        except Exception as e:
            logger.error(f"Error adding camera to existing tab: {e}")

    @Property(list, notify=availableTabsChanged)
    def availableTabs(self):
        """Get list of available saved view and virtual window tabs directly from gridManager"""
        try:
            available_tabs = []

            # Get data directly from gridManager instead of tree view
            logger.debug(f"🔍 DEBUG: Getting tabs from gridManager.data, count = {len(gridManager.data)}")

            for grid_id, grid_model in gridManager.data.items():
                logger.debug(f"🔍 DEBUG: Processing grid {grid_id}: {grid_model.get_property('name')} type={grid_model.get_property('type')}")

                # Check if this is a SavedView or VirtualWindow
                grid_type = grid_model.get_property('type')
                if grid_type in [TabType.SavedView, TabType.VirtualWindow]:
                    # Get grid information from the model's currentGrid property
                    rows = 3  # default
                    columns = 3  # default

                    # Try to get grid size from currentGrid property
                    current_grid = grid_model.get_property('currentGrid')
                    logger.debug(f"🔍 DEBUG: currentGrid = {current_grid}")

                    if current_grid:
                        try:
                            if isinstance(current_grid, str) and current_grid != "{}":
                                # Parse JSON string
                                import json
                                grid_config = json.loads(current_grid.replace("'", '"'))
                                if 'row' in grid_config and 'col' in grid_config:
                                    rows = int(grid_config['row'])
                                    columns = int(grid_config['col'])
                                    logger.debug(f"🔍 DEBUG: Parsed grid size from string: {columns}x{rows}")
                            elif isinstance(current_grid, dict):
                                # Direct dict access
                                if 'row' in current_grid and 'col' in current_grid:
                                    rows = int(current_grid['row'])
                                    columns = int(current_grid['col'])
                                    logger.debug(f"🔍 DEBUG: Got grid size from dict: {columns}x{rows}")
                        except Exception as e:
                            logger.debug(f"Error parsing currentGrid for {grid_model.get_property('name')}: {e}")

                    # Convert TabType to string for QML
                    type_string = 'SavedView' if grid_type == TabType.SavedView else 'VirtualWindow'

                    tab_info = {
                        'name': grid_model.get_property('name'),
                        'rows': rows,
                        'columns': columns,
                        'id': grid_model.get_property('id'),
                        'type': type_string
                    }
                    available_tabs.append(tab_info)
                    logger.debug(f"🔍 DEBUG: Added tab: {tab_info}")

            logger.debug(f"🔍 DEBUG: Final result - Found {len(available_tabs)} tabs: {[tab['name'] + ' (' + tab['type'] + ') ' + str(tab['columns']) + 'x' + str(tab['rows']) for tab in available_tabs]}")
            return available_tabs

        except Exception as e:
            logger.error(f"Error getting available tabs from gridManager: {e}")
            logger.error(f"Exception details: {traceback.format_exc()}")
            return []

    @Slot()
    def refreshAvailableTabs(self):
        """Force refresh available tabs - can be called from QML"""
        try:
            logger.debug("🔍 DEBUG: Force refreshing available tabs from QML")
            self.availableTabsChanged.emit()
        except Exception as e:
            logger.error(f"Error force refreshing available tabs: {e}")

    @Slot(str, result='QVariantList')
    def getAvailablePositionsForTab(self, tab_id):
        """Get list of available positions for a specific tab"""
        try:
            # Find the target grid model by tab_id
            target_grid_model = None
            for grid_id, grid_model in gridManager.data.items():
                if grid_model.get_property('id') == tab_id:
                    target_grid_model = grid_model
                    break

            if not target_grid_model:
                logger.warning(f"Grid model for tab ID '{tab_id}' not found")
                return []

            # Get current grid data
            current_grid = target_grid_model.get_property('currentGrid')
            rows = 3  # default
            columns = 3  # default

            # Parse grid size
            if current_grid:
                try:
                    if isinstance(current_grid, str) and current_grid != "{}":
                        import json
                        grid_config = json.loads(current_grid.replace("'", '"'))
                        if 'row' in grid_config and 'col' in grid_config:
                            rows = int(grid_config['row'])
                            columns = int(grid_config['col'])
                    elif isinstance(current_grid, dict):
                        if 'row' in current_grid and 'col' in current_grid:
                            rows = int(current_grid['row'])
                            columns = int(current_grid['col'])
                except Exception as e:
                    logger.debug(f"Error parsing currentGrid for tab '{tab_id}': {e}")

            # Get active cells for this grid (from the grid model's _active_cells)
            active_cells = target_grid_model._active_cells if hasattr(target_grid_model, '_active_cells') else {}

            # Calculate total positions and find available ones
            total_positions = rows * columns
            available_positions = []

            for position in range(total_positions):
                # Position is available if it's not in active_cells or value is None
                if position not in active_cells or active_cells[position] is None:
                    available_positions.append(position)

            logger.debug(f"Found {len(available_positions)} available positions for tab '{tab_id}': {available_positions[:10]}{'...' if len(available_positions) > 10 else ''}")
            return available_positions

        except Exception as e:
            logger.error(f"Error getting available positions for tab '{tab_id}': {e}")
            logger.error(f"Exception details: {traceback.format_exc()}")
            return []

    @Slot(tuple)
    def _onTabAdded(self, data):
        """Handle when a tab is added"""
        try:
            logger.debug("Tab added, updating available tabs")
            self.availableTabsChanged.emit()
        except Exception as e:
            logger.error(f"Error handling tab added signal: {e}")

    @Slot(tuple)
    def _onTabListAdded(self, data):
        """Handle when a list of tabs is added"""
        try:
            logger.debug("Tab list added, updating available tabs")
            self.availableTabsChanged.emit()
        except Exception as e:
            logger.error(f"Error handling tab list added signal: {e}")

    @Slot(int)
    def updateCameraCount(self, count):
        """Update camera count from QML and emit signal if changed"""
        if self._camera_count != count:
            self._camera_count = count
            logger.debug(f"Camera count updated from QML: {self._camera_count}")
            self.cameraCountChanged.emit(self._camera_count)

    @Slot(str, result=int)
    def getGroupCameraCount(self, group_id):
        """Get camera count for a group by group ID"""
        try:
            from src.common.model.group_model import group_model_manager

            # Get group model from group_model_manager
            group_model = group_model_manager.get_group_model(id=group_id)
            if not group_model:
                logger.warning(f"Group not found: {group_id}")
                return 1  # Default fallback

            # Get camera IDs from group model
            camera_ids = group_model.get_property("cameraIds", [])
            camera_count = len(camera_ids) if camera_ids else 1

            logger.debug(f"Group {group_id} contains {camera_count} cameras")
            return camera_count

        except Exception as e:
            logger.error(f"Error getting group camera count for {group_id}: {e}")
            return 1  # Default fallback



    @Property(str, notify=backgroundColorChanged)
    def backgroundColor(self):
        return self._background_color

    @backgroundColor.setter
    def backgroundColor(self, value):
        if self._background_color != value:
            self._background_color = value
            self.backgroundColorChanged.emit()

    @Property(str, notify=foregroundColorChanged)
    def foregroundColor(self):
        return self._foreground_color

    @foregroundColor.setter
    def foregroundColor(self, value):
        if self._foreground_color != value:
            self._foreground_color = value
            self.foregroundColorChanged.emit()

    @Property(str, notify=borderColorChanged)
    def borderColor(self):
        return self._border_color

    @borderColor.setter
    def borderColor(self, value):
        if self._border_color != value:
            self._border_color = value
            self.borderColorChanged.emit()

    @Property(str, notify=headerColorChanged)
    def headerColor(self):
        return self._header_color

    @headerColor.setter
    def headerColor(self, value):
        if self._header_color != value:
            self._header_color = value
            self.headerColorChanged.emit()

    @Property(str, notify=itemBackgroundColorChanged)
    def itemBackgroundColor(self):
        return self._item_background_color

    @itemBackgroundColor.setter
    def itemBackgroundColor(self, value):
        if self._item_background_color != value:
            self._item_background_color = value
            self.itemBackgroundColorChanged.emit()

    @Property(str, notify=hoverColorChanged)
    def hoverColor(self):
        return self._hover_color

    @hoverColor.setter
    def hoverColor(self, value):
        if self._hover_color != value:
            self._hover_color = value
            self.hoverColorChanged.emit()

    @Property(str, notify=dragActiveColorChanged)
    def dragActiveColor(self):
        return self._drag_active_color

    @dragActiveColor.setter
    def dragActiveColor(self, value):
        if self._drag_active_color != value:
            self._drag_active_color = value
            self.dragActiveColorChanged.emit()

    @Property(str, notify=videoBackgroundColorChanged)
    def videoBackgroundColor(self):
        return self._video_background_color

    @videoBackgroundColor.setter
    def videoBackgroundColor(self, value):
        if self._video_background_color != value:
            self._video_background_color = value
            self.videoBackgroundColorChanged.emit()

    @Property('QVariantList', notify=aiSolutionsChanged)
    def aiSolutions(self):
        # Đảm bảo luôn trả về một danh sách hợp lệ
        if not hasattr(self, '_ai_solutions') or self._ai_solutions is None:
            return []
        return self._ai_solutions


    @aiSolutions.setter
    def aiSolutions(self, value):
        # Đảm bảo giá trị là một danh sách hợp lệ
        if not isinstance(value, list):
            logger.warning(f"Attempting to set aiSolutions with non-list value: {type(value)}")
            value = []

        if self._ai_solutions != value:
            self._ai_solutions = value
            try:
                self.aiSolutionsChanged.emit()
            except Exception as e:
                logger.error(f"Error emitting aiSolutionsChanged: {e}")
                logger.error(traceback.format_exc())

    @Slot()
    def fetchAiSolutions(self):
        """Fetch AI solutions from the API"""
        # Nếu đang tải hoặc đã tải xong, không tải lại
        if self._ai_solutions_loading:
            logger.debug("AI solutions are already being loaded")
            return

        # Nếu đã tải xong và có dữ liệu, không tải lại
        if self._ai_solutions_loaded and len(self._ai_solutions) > 0:
            logger.debug("Using cached AI solutions")
            return

        self._ai_solutions_loading = True

        # Tạo một thread để tải dữ liệu
        from PySide6.QtCore import QThread, Signal

        class AiSolutionsThread(QThread):
            finished = Signal(list)
            error = Signal(str)

            def __init__(self):
                super().__init__()
                # Đặt mức ưu tiên thấp để không ảnh hưởng đến UI
                self.setPriority(QThread.LowPriority)

            def run(self):
                try:
                    # Get the current controller
                    controller = main_controller.current_controller
                    logger.debug(f"Fetching AI solutions with controller: {controller}")
                    if not controller:
                        logger.error("No controller available")
                        self.error.emit("No controller available")
                        return

                    # Kiểm tra controller và lấy server_ip
                    if not hasattr(controller, 'server') or not controller.server:
                        logger.error("Controller has no server attribute or server is None")
                        self.error.emit("Controller has no server")
                        return

                    if not hasattr(controller.server, 'data') or not controller.server.data:
                        logger.error("Server has no data attribute or data is None")
                        self.error.emit("Server has no data")
                        return

                    if not hasattr(controller.server.data, 'server_ip') or not controller.server.data.server_ip:
                        logger.error("Server data has no server_ip attribute or server_ip is None")
                        self.error.emit("Server has no IP")
                        return

                    server_ip = controller.server.data.server_ip
                    logger.debug(f"Using server IP: {server_ip}")

                    # Kiểm tra aiflow_model_manager
                    if not hasattr(aiflow_model_manager, 'aiflows'):
                        logger.error("aiflow_model_manager has no aiflows attribute")
                        self.error.emit("AI flow manager has no flows")
                        return

                    # Lấy dữ liệu từ API nếu cần
                    if server_ip not in aiflow_model_manager.aiflows:
                        logger.debug(f"Server IP {server_ip} not in aiflow_model_manager.aiflows. Available servers: {list(aiflow_model_manager.aiflows.keys())}")
                        try:
                            logger.debug("Calling API to get AI flows")
                            ai_flows_list = controller.get_aiflows()
                            if not ai_flows_list:
                                logger.warning("No AI flows returned from API")
                                self.finished.emit([])
                                return
                            logger.debug(f"Successfully fetched {len(ai_flows_list)} AI flows from API")
                        except Exception as api_error:
                            logger.error(f"Error fetching AI flows from API: {api_error}")
                            logger.error(traceback.format_exc())
                            self.error.emit(f"API error: {str(api_error)}")
                            return

                    # Lấy dữ liệu từ manager
                    ai_flows = aiflow_model_manager.get_aiflows(server_ip=server_ip)
                    if not ai_flows:
                        logger.warning("No AI flows found in manager")
                        self.finished.emit([])
                        return

                    # Xử lý dữ liệu an toàn
                    ai_solutions = []
                    try:
                        for ai_flow_id, ai_flow in ai_flows.items():
                            # Kiểm tra dữ liệu hợp lệ trước khi thêm vào danh sách
                            if not ai_flow or not hasattr(ai_flow, 'data') or not ai_flow.data:
                                logger.warning(f"Invalid AI flow data for ID {ai_flow_id}")
                                continue

                            # Lấy tên và loại an toàn
                            name = getattr(ai_flow.data, 'name', "Unknown")
                            flow_type = getattr(ai_flow.data, 'type', "Unknown")

                            ai_solutions.append({
                                "id": ai_flow_id,
                                "name": name,
                                "type": flow_type
                            })

                        logger.debug(f"Processed {len(ai_solutions)} AI solutions")
                        self.finished.emit(ai_solutions)
                    except Exception as process_error:
                        logger.error(f"Error processing AI flows: {process_error}")
                        logger.error(traceback.format_exc())
                        self.error.emit(f"Processing error: {str(process_error)}")
                except Exception as e:
                    logger.error(f"Exception in AiSolutionsThread.run: {e}")
                    logger.error(traceback.format_exc())
                    self.error.emit(str(e))

        # Tạo và khởi động thread
        thread = AiSolutionsThread()

        def on_finished(ai_solutions):
            try:
                # Đảm bảo ai_solutions là một danh sách hợp lệ
                if not isinstance(ai_solutions, list):
                    logger.error(f"Invalid AI solutions type: {type(ai_solutions)}")
                    self.aiSolutions = []
                else:
                    self.aiSolutions = ai_solutions

                self._ai_solutions_loaded = True
                self._ai_solutions_loading = False
                logger.debug(f"Fetched {len(ai_solutions)} AI solutions")
            except Exception as e:
                logger.error(f"Error in on_finished: {e}")
                logger.error(traceback.format_exc())
                self._ai_solutions_loading = False
                self.aiSolutions = []
            finally:
                thread.deleteLater()

        def on_error(error_msg):
            try:
                self._ai_solutions_loading = False
                logger.error(f"Error fetching AI solutions: {error_msg}")
                self.aiSolutions = []
                self._ai_solutions_error = error_msg
                self.aiSolutionsError.emit(error_msg)
            except Exception as e:
                logger.error(f"Error in on_error: {e}")
                logger.error(traceback.format_exc())
            finally:
                thread.deleteLater()

        thread.finished.connect(on_finished)
        thread.error.connect(on_error)
        thread.start()

    @Slot(int, str)
    def applyAiSolution(self, position, ai_flow_id):
        """Apply an AI solution to a camera

        Args:
            position: The position of the camera in the grid
            ai_flow_id: The ID of the AI flow to apply
        """
        try:
            # Validate input parameters
            if not isinstance(position, int) or position < 0:
                logger.error(f"Invalid position: {position}")
                return

            if not ai_flow_id:
                logger.error("AI flow ID cannot be empty")
                return

            # Check if the position is valid and has a camera
            if position not in self._active_cells:
                logger.warning(f"No camera found at position {position}")
                return

            # Get the camera model directly
            camera_model = self._active_cells[position]
            if not camera_model or not hasattr(camera_model, 'data') or not camera_model.data:
                logger.warning(f"No valid camera model found for position {position}")
                return

            # Get the camera ID from the model
            camera_id = camera_model.get_property('id')

            # Get the current controller
            controller = main_controller.current_controller
            if not controller:
                logger.warning("No controller available")
                return

            # Prepare data for API call
            data = {
                "cameraId": camera_id,
                "aiFlowId": ai_flow_id
            }

            # Call the API to apply the AI solution
            try:
                response = controller.api_client.apply_ai_flow_camera(data)
                if response and response.status_code == 200:
                    logger.debug(f"Successfully applied AI solution {ai_flow_id} to camera {camera_id}")
                    # Refresh AI flows to update the UI
                    controller.get_aiflows()
                    # Refresh local cache
                    self._ai_solutions_loaded = False
                    QTimer.singleShot(1000, self.fetchAiSolutions)
                else:
                    error_msg = response.text if response else "Unknown error"
                    logger.warning(f"Failed to apply AI solution {ai_flow_id} to camera {camera_id}: {error_msg}")
            except Exception as api_error:
                logger.error(f"API error when applying AI solution: {api_error}")
                logger.error(traceback.format_exc())
        except Exception as e:
            logger.error(f"Error applying AI solution: {e}")
            logger.error(traceback.format_exc())


    @Slot(int)
    def showCameraInfo(self, position):
        """Show camera information dialog for the camera at the specified position

        Args:
            position: The position of the camera in the grid
        """
        try:
            # Check if the position is valid and has a camera
            if position not in self._active_cells:
                logger.warning(f"No camera found at position {position}")
                return

            # Get the camera model directly
            camera_model = self._active_cells[position]
            if not camera_model or not hasattr(camera_model, 'data') or not camera_model.data:
                logger.warning(f"No valid camera model found for position {position}")
                return

            # Show the camera info dialog
            from src.common.widget.dialogs.camera_info_dialog import CameraInfoDialog
            from src.common.controller.main_controller import main_controller

            # Get the parent widget (usually CameraScreen)
            parent = main_controller.list_parent.get('CameraScreen')
            if not parent:
                logger.warning("Parent widget not found")
                parent = None

            # Create and show the dialog
            camera_info = CameraInfoDialog(parent=parent, data=camera_model)
            camera_info.exec()

        except Exception as e:
            logger.error(f"Error showing camera info: {e}")
            logger.error(traceback.format_exc())

    @Slot(int, 'QPoint', 'QPoint')
    @Slot(int, int, int, int, int)
    def controlPTZ(self, position, arg1=None, arg2=None, arg3=None, arg4=None):
        """Control PTZ movement for the camera at the specified position

        Args:
            position: The position of the camera in the grid
            arg1, arg2, arg3, arg4: Can be either:
                - start_point, end_point: QPoint representing the start and end points of the PTZ movement
                - 0, 0, x, y: Integers representing the direction (used by direction buttons)
        """
        try:
            # Kiểm tra xem đây là lệnh gọi từ nút điều khiển hay từ PTZ3D
            if isinstance(arg1, int) and isinstance(arg2, int) and isinstance(arg3, int) and isinstance(arg4, int):
                # Lệnh gọi từ nút điều khiển
                x = arg3
                y = arg4

                # Validate input parameters
                if not isinstance(position, int) or position < 0:
                    logger.error(f"Invalid position: {position}")
                    return

                # Check if the position is valid and has a camera
                if position not in self._active_cells:
                    logger.warning(f"No camera found at position {position}")
                    return

                # Get the camera model directly
                camera_model = self._active_cells[position]
                if not camera_model or not hasattr(camera_model, 'data') or not camera_model.data:
                    logger.warning(f"No valid camera model found for position {position}")
                    return

                # Get the camera ID from the model
                camera_id = camera_model.get_property('id')

                # Get the controller for this camera
                server_ip = camera_model.get_property("server_ip")
                # Import controller_manager here to avoid circular import
                from src.common.controller.controller_manager import controller_manager
                controller = controller_manager.get_controller(server_ip=server_ip)
                if not controller:
                    logger.warning(f"Controller not found for camera: {camera_id}")
                    return

                # Kiểm tra xem camera có hỗ trợ PTZ không
                if not hasattr(camera_model, 'data') or not camera_model.data:
                    logger.warning(f"Camera model has no data attribute: {camera_id}")
                    return

                # Kiểm tra xem camera có hỗ trợ PTZ không
                if not camera_model.get_property("ptzCap") or len(camera_model.get_property("ptzCap", [])) == 0:
                    logger.warning(f"Camera {camera_id} does not support PTZ")
                    return

                # Kiểm tra loại camera để gọi API phù hợp
                if camera_model.get_property("type") == "AVIGILON":
                    data = {
                        "cameraId": camera_id,
                        "endPoint": "/camera/commands/pan-tilt-zoom",
                        "requestData": {
                            "continuous": {
                                "panAmount": x * self._ptz_speed,
                                "tiltAmount": -y * self._ptz_speed,
                                "zoomAmount": 0,
                                "action": "START"
                            }
                        }
                    }
                    controller.forward_avigilon_ptz(cameraId=data["cameraId"], requestData=data["requestData"])
                    logger.debug(f"Sent Avigilon PTZ command from button: {data}, speed={self._ptz_speed}")
                else:
                    # Gọi PTZ continuous move với tọa độ đã tính toán và nhân với speed
                    # Sử dụng self._ptz_speed để điều chỉnh tốc độ di chuyển
                    controller.ptz_continuous_move(
                        parent=self,
                        cameraId=camera_id,
                        x=x * self._ptz_speed,
                        y=y * self._ptz_speed,
                        zoom=0
                    )
                    logger.debug(f"Sent PTZ continuous move from button: x={x * self._ptz_speed}, y={y * self._ptz_speed}, speed={self._ptz_speed}")
            else:
                # Lệnh gọi từ PTZ3D
                start_point = arg1
                end_point = arg2

                # Validate input parameters
                if not isinstance(position, int) or position < 0:
                    logger.error(f"Invalid position: {position}")
                    return

                # Check if the position is valid and has a camera
                if position not in self._active_cells:
                    logger.warning(f"No camera found at position {position}")
                    return

                # Get the camera model directly
                camera_model = self._active_cells[position]
                if not camera_model or not hasattr(camera_model, 'data') or not camera_model.data:
                    logger.warning(f"No valid camera model found for position {position}")
                    return

                # Get the camera ID from the model
                camera_id = camera_model.get_property('id')

                # Get the controller for this camera
                server_ip = camera_model.get_property("server_ip")
                # Import controller_manager here to avoid circular import
                from src.common.controller.controller_manager import controller_manager
                controller = controller_manager.get_controller(server_ip=server_ip)
                if not controller:
                    logger.warning(f"Controller not found for camera: {camera_id}")
                    return

                # Kiểm tra xem camera có hỗ trợ PTZ không
                if not hasattr(camera_model, 'data') or not camera_model.data:
                    logger.warning(f"Camera model has no data attribute: {camera_id}")
                    return

                # Kiểm tra xem camera có hỗ trợ PTZ không
                if not camera_model.get_property("ptzCap") or len(camera_model.get_property("ptzCap", [])) == 0:
                    logger.warning(f"Camera {camera_id} does not support PTZ")
                    return

                # Lấy kích thước thực tế của video
                width = getattr(self, 'rectangle_widget', None)
                if width and hasattr(width, 'real_width'):
                    width = width.real_width
                else:
                    width = 640  # Giá trị mặc định

                height = getattr(self, 'rectangle_widget', None)
                if height and hasattr(height, 'real_height'):
                    height = height.real_height
                else:
                    height = 480  # Giá trị mặc định

                # Xác định loại camera để lấy dữ liệu hiệu chuẩn phù hợp
                calib_data = None
                if camera_model.get_property("cameraBranch") == "Avigilon":
                    calib_data = CalibData.get_data(type=Manufacturer.Avigilon)
                else:
                    calib_data = CalibData.get_data(type=Manufacturer.Invalid)

                # Tính toán tọa độ PTZ dựa trên điểm bắt đầu và kết thúc
                coordinate = CalibData.get_coordinate_ptz_arrow(
                    start_point=start_point,
                    end_point=end_point,
                    width=width,
                    height=height,
                    calib_data=calib_data
                )

                # Kiểm tra loại camera để gọi API phù hợp
                if camera_model.get_property("type") == "AVIGILON":
                    data = {
                        "cameraId": camera_id,
                        "endPoint": "/camera/commands/pan-tilt-zoom",
                        "requestData": {
                            "continuous": {
                                "panAmount": coordinate['pan'] * self._ptz_speed,
                                "tiltAmount": -coordinate['tilt'] * self._ptz_speed,
                                "zoomAmount": coordinate['zoom'],
                                "action": "START"
                            }
                        }
                    }
                    controller.forward_avigilon_ptz(cameraId=data["cameraId"], requestData=data["requestData"])
                    logger.debug(f"Sent Avigilon PTZ command: {data}, speed={self._ptz_speed}")
                else:
                    # Gọi PTZ continuous move với tọa độ đã tính toán và nhân với speed
                    # Sử dụng self._ptz_speed để điều chỉnh tốc độ di chuyển
                    controller.ptz_continuous_move(
                        parent=self,
                        cameraId=camera_id,
                        x=coordinate['pan'] * self._ptz_speed,
                        y=coordinate['tilt'] * self._ptz_speed,
                        zoom=coordinate['zoom']
                    )
                    logger.debug(f"Sent PTZ continuous move: pan={coordinate['pan'] * self._ptz_speed}, tilt={coordinate['tilt'] * self._ptz_speed}, zoom={coordinate['zoom']}, speed={self._ptz_speed}")

        except Exception as e:
            logger.error(f"Error controlling PTZ: {e}")
            logger.error(traceback.format_exc())

    @Slot(int)
    def stopPTZ(self, position):
        """Stop PTZ movement for the camera at the specified position

        Args:
            position: The position of the camera in the grid
        """
        logger.debug(f"stopPTZ called for position {position}")
        try:
            # Validate input parameters
            if not isinstance(position, int) or position < 0:
                logger.error(f"Invalid position: {position}")
                return

            # Check if the position is valid and has a camera
            if position not in self._active_cells:
                logger.warning(f"No camera found at position {position}")
                return

            # Get the camera model directly
            camera_model = self._active_cells[position]
            if not camera_model or not hasattr(camera_model, 'data') or not camera_model.data:
                logger.warning(f"No valid camera model found for position {position}")
                return

            # Get the camera ID from the model
            camera_id = camera_model.get_property('id')

            # Get the controller for this camera
            server_ip = camera_model.get_property('server_ip')
            # Import controller_manager here to avoid circular import
            from src.common.controller.controller_manager import controller_manager
            controller = controller_manager.get_controller(server_ip=server_ip)
            if not controller:
                logger.warning(f"Controller not found for camera: {camera_id}")
                return

            # Kiểm tra xem camera có hỗ trợ PTZ không
            if not camera_model.get_property("ptzCap") or len(camera_model.get_property("ptzCap", [])) == 0:
                logger.warning(f"Camera {camera_id} does not support PTZ")
                return

            # Kiểm tra loại camera để gọi API phù hợp
            if camera_model.get_property("type") == "AVIGILON":
                data = {
                    "cameraId": camera_id,
                    "endPoint": "/camera/commands/pan-tilt-zoom",
                    "requestData": {
                        "continuous": {
                            "panAmount": 0,
                            "tiltAmount": 0,
                            "zoomAmount": 0,
                            "action": "STOP"
                        }
                    }
                }
                controller.forward_avigilon_ptz(cameraId=data["cameraId"], requestData=data["requestData"])
                logger.debug(f"Sent Avigilon PTZ stop command for camera {camera_id}")
            else:
                # Gọi API dừng PTZ
                controller.ptz_stop(cameraId=camera_id)
                logger.debug(f"Sent PTZ stop command for camera {camera_id}")

            # Nếu có queue đang xử lý, đặt None vào queue để dừng thread
            if hasattr(self, 'coordinate_queue') and hasattr(self, 'process_queue_thread'):
                if self.process_queue_thread.isRunning():
                    self.coordinate_queue.put(None)
                    logger.debug(f"Added stop signal to coordinate queue for camera {camera_id}")

        except Exception as e:
            logger.error(f"Error stopping PTZ: {e}")
            logger.error(traceback.format_exc())

    @Slot(int, float)
    def setPTZSpeed(self, position, speed):
        """Set PTZ speed for the camera at the specified position

        Args:
            position: The position of the camera in the grid
            speed: Speed factor (0.0 to 1.0)
        """
        try:
            # Validate input parameters
            if not isinstance(position, int) or position < 0:
                logger.error(f"Invalid position: {position}")
                return

            # Check if the position is valid and has a camera
            if position not in self._active_cells:
                logger.warning(f"No camera found at position {position}")
                return

            # Get the camera model directly
            camera_model = self._active_cells[position]
            if not camera_model or not hasattr(camera_model, 'data') or not camera_model.data:
                logger.warning(f"No valid camera model found for position {position}")
                return

            # Get the camera ID from the model
            camera_id = camera_model.get_property('id')

            # Get the controller for this camera
            server_ip = camera_model.get_property("server_ip")
            # Import controller_manager here to avoid circular import
            from src.common.controller.controller_manager import controller_manager
            controller = controller_manager.get_controller(server_ip=server_ip)
            if not controller:
                logger.warning(f"Controller not found for camera: {camera_id}")
                return

            # Kiểm tra xem camera có hỗ trợ PTZ không
            if not hasattr(camera_model, 'data') or not camera_model.data:
                logger.warning(f"Camera model has no data attribute: {camera_id}")
                return

            # Kiểm tra xem camera có hỗ trợ PTZ không
            if not camera_model.get_property("ptzCap") or len(camera_model.get_property("ptzCap", [])) == 0:
                logger.warning(f"Camera {camera_id} does not support PTZ")
                return

            # Lưu tốc độ PTZ để sử dụng cho các lệnh di chuyển tiếp theo
            self._ptz_speed = speed
            logger.debug(f"Set PTZ speed for camera {camera_id} to {speed}")

        except Exception as e:
            logger.error(f"Error setting PTZ speed: {e}")
            logger.error(traceback.format_exc())


    @Slot()
    def stopPTZZoom(self):
        """Stop PTZ zoom movement for the current camera"""
        try:
            # Kiểm tra xem có thông tin camera hiện tại không
            if not hasattr(self, '_current_zoom_camera') or self._current_zoom_camera is None:
                logger.warning("No current zoom camera information available")
                return

            # Lấy thông tin camera hiện tại
            camera_info = self._current_zoom_camera

            # Kiểm tra loại camera để gọi API phù hợp
            if camera_info["is_avigilon"]:
                data = {
                    "cameraId": camera_info["camera_id"],
                    "endPoint": "/camera/commands/pan-tilt-zoom",
                    "requestData": {
                        "continuous": {
                            "panAmount": 0,
                            "tiltAmount": 0,
                            "zoomAmount": 0,
                            "action": "STOP"
                        }
                    }
                }
                camera_info["controller"].forward_avigilon_ptz(cameraId=data["cameraId"], requestData=data["requestData"])
                logger.debug(f"Stopped Avigilon PTZ zoom for camera: {camera_info['camera_id']}")
            else:
                # Gọi PTZ stop
                camera_info["controller"].ptz_stop(cameraId=camera_info["camera_id"])
                logger.debug(f"Stopped PTZ zoom for camera: {camera_info['camera_id']}")

            # Xóa thông tin camera hiện tại
            self._current_zoom_camera = None

        except Exception as e:
            logger.error(f"Error stopping PTZ zoom: {e}")
            logger.error(traceback.format_exc())

    @Slot(int, float, float, float, float, int, int)
    def setDragToZoom(self, position, start_x, start_y, end_x, end_y, width, height):
        """Control drag-to-zoom for the camera at the specified position

        Args:
            position: The position of the camera in the grid
            start_x: X coordinate of the start point
            start_y: Y coordinate of the start point
            end_x: X coordinate of the end point
            end_y: Y coordinate of the end point
            width: Width of the camera view
            height: Height of the camera view
        """
        try:
            # Validate input parameters
            if not isinstance(position, int) or position < 0:
                logger.error(f"Invalid position: {position}")
                return

            # Check if the position is valid and has a camera
            if position not in self._active_cells:
                logger.warning(f"No camera found at position {position}")
                return

            # Get the camera model directly
            camera_model = self._active_cells[position]
            if not camera_model or not hasattr(camera_model, 'data') or not camera_model.data:
                logger.warning(f"No valid camera model found for position {position}")
                return

            # Get the camera ID from the model
            camera_id = camera_model.get_property('id')

            # Get the controller for this camera
            server_ip = camera_model.get_property('server_ip')
            # Import controller_manager here to avoid circular import
            from src.common.controller.controller_manager import controller_manager
            controller = controller_manager.get_controller(server_ip=server_ip)
            if not controller:
                logger.warning(f"Controller not found for camera: {camera_id}")
                return

            # Kiểm tra xem camera có hỗ trợ PTZ không
            if not camera_model.get_property("ptzCap") or len(camera_model.get_property("ptzCap", [])) == 0:
                logger.warning(f"Camera {camera_id} does not support PTZ")
                return

            # Xác định loại camera để lấy dữ liệu hiệu chuẩn phù hợp
            calib_data = None
            if camera_model.get_property("cameraBranch") == "Avigilon":
                calib_data = CalibData.get_data(type=Manufacturer.Avigilon)
            else:
                calib_data = CalibData.get_data(type=Manufacturer.Invalid)

            # Tạo QPoint objects từ các tọa độ
            start_point = QPoint(int(start_x), int(start_y))
            end_point = QPoint(int(end_x), int(end_y))
            # Gọi hàm set_drag_to_zoom từ controller
            controller.set_drag_to_zoom(
                parent=self,
                cameraId=camera_id,
                start_point=start_point,
                end_point=end_point,
                width=width,
                height=height,
                calib_data=calib_data
            )
            logger.debug(f"Sent drag-to-zoom command for camera {camera_id}")

        except Exception as e:
            logger.error(f"Error setting drag-to-zoom: {e}")
            logger.error(traceback.format_exc())

    @Slot(int, float)
    def controlPTZZoom(self, position, factor):
        """Control PTZ zoom for the camera at the specified position

        Args:
            position: The position of the camera in the grid
            factor: Zoom factor (positive for zoom in, negative for zoom out)
        """
        try:
            # Validate input parameters
            if not isinstance(position, int) or position < 0:
                logger.error(f"Invalid position: {position}")
                return

            # Check if the position is valid and has a camera
            if position not in self._active_cells:
                logger.warning(f"No camera found at position {position}")
                return

            # Get the camera model directly
            camera_model = self._active_cells[position]
            if not camera_model or not hasattr(camera_model, 'data') or not camera_model.data:
                logger.warning(f"No valid camera model found for position {position}")
                return

            # Get the camera ID from the model
            camera_id = camera_model.get_property('id')

            # Get the controller for this camera
            server_ip = camera_model.get_property("server_ip")
            # Import controller_manager here to avoid circular import
            from src.common.controller.controller_manager import controller_manager
            controller = controller_manager.get_controller(server_ip=server_ip)
            if not controller:
                logger.warning(f"Controller not found for camera: {camera_id}")
                return

            # Kiểm tra xem camera có hỗ trợ PTZ không
            if not hasattr(camera_model, 'data') or not camera_model.data:
                logger.warning(f"Camera model has no data attribute: {camera_id}")
                return

            # Kiểm tra xem camera có hỗ trợ PTZ không
            if not camera_model.get_property("ptzCap") or len(camera_model.get_property("ptzCap", [])) == 0:
                logger.warning(f"Camera {camera_id} does not support PTZ")
                return

            # Lưu thông tin camera hiện tại để dừng zoom sau đó
            self._current_zoom_camera = {
                "position": position,
                "camera_id": camera_id,
                "controller": controller,
                "is_avigilon": camera_model.get_property("type") == "AVIGILON"
            }

            # Lấy kích thước của video để tính toán tọa độ trung tâm
            # Sử dụng giá trị mặc định nếu không tìm thấy
            width = 640  # Giá trị mặc định
            height = 480  # Giá trị mặc định

            # Tạo điểm bắt đầu và điểm kết thúc ở trung tâm
            center_x = width / 2
            center_y = height / 2

            # Tạo điểm bắt đầu và điểm kết thúc để zoom vào tâm
            start_point = QPoint(center_x, center_y)
            end_point = QPoint(center_x, center_y)

            # Sử dụng phương thức tương tự như trong camera_widget.py
            if camera_model.get_property("type") == "AVIGILON":
                # Đối với camera Avigilon, sử dụng continuous move
                data = {
                    "cameraId": camera_id,
                    "endPoint": "/camera/commands/pan-tilt-zoom",
                    "requestData": {
                        "continuous": {
                            "panAmount": 0,
                            "tiltAmount": 0,
                            "zoomAmount": factor,
                            "action": "START"
                        }
                    }
                }
                controller.forward_avigilon_ptz(cameraId=data["cameraId"], requestData=data["requestData"])
                logger.debug(f"Sent Avigilon PTZ zoom command: {data}")
            else:
                # Đối với các camera khác, sử dụng continuous move
                controller.ptz_continuous_move(
                    parent=self,
                    cameraId=camera_id,
                    x=0,  # Không di chuyển theo trục x
                    y=0,  # Không di chuyển theo trục y
                    zoom=factor  # Sử dụng hệ số zoom trực tiếp
                )
                logger.debug(f"Sent PTZ continuous zoom: factor={factor}")

            # Không sử dụng timer để dừng zoom
            # Camera sẽ tiếp tục zoom cho đến khi người dùng dừng lại
            # Việc dừng zoom sẽ được xử lý bởi sự kiện onReleased hoặc onExited trong PTZ3DControl.qml

        except Exception as e:
            logger.error(f"Error controlling PTZ zoom: {e}")
            logger.error(traceback.format_exc())

    def saveCurrentData(self):
        valid_cells = {k: v for k, v in self._active_cells.items() if v is not None}

        if len(valid_cells) != len(self._active_cells):
            logger.debug(f'Cleaning up _active_cells: removed {len(self._active_cells) - len(valid_cells)} invalid entries')
            self._active_cells = valid_cells

        listGridData = {}

        primary_cameras = {}

        # Đầu tiên, tìm tất cả các camera chính (có trong _cell_dimensions)
        for key in self._cell_dimensions.keys():
            if key in self._active_cells:
                item = self._active_cells[key]
                camera_id = item.get_property('id') if hasattr(item, 'get_property') else "Unknown"
                cell_dims = self._cell_dimensions.get(key, {"width": 1, "height": 1})

                # Lưu camera chính và kích thước
                primary_cameras[key] = {
                    "camera_model": item,
                    "camera_id": camera_id,
                    "width": cell_dims["width"],
                    "height": cell_dims["height"]
                }

                logger.debug(f"Found primary camera {camera_id} at position {key} with size {cell_dims['width']}x{cell_dims['height']}")

        # In ra số lượng camera chính
        logger.debug(f"Total primary cameras: {len(primary_cameras)}")

        # Lưu tất cả camera chính vào listGridData
        for key, camera_info in primary_cameras.items():
            # Store camera data in our grid data structure
            listGridData[key] = {
                "type": CommonEnum.ItemType.CAMERA,
                "position": key,
                "camera_id": camera_info["camera_id"],
                "width": camera_info["width"],
                "height": camera_info["height"]
            }
            logger.debug(f"Saved camera {camera_info['camera_id']} at position {key} with size {camera_info['width']}x{camera_info['height']}")

        # Save grid configuration to tab model
        from datetime import datetime
        import time

        grid_config = {
            "row": self._rows,
            "col": self._columns,
            "timestamp": int(time.time()),
            "name": f"View_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        }

        self._currentData["currentGrid"] = str(grid_config)
        self._currentData["listGridData"] = str(listGridData)

    def shortcut_activated(self):
        self.saveCurrentData()
        logger.info(f'shortcut_activated = {self.isSave}')
        if self.isSave:
            logger.info(f'View already saved, skipping save operation')
            return
        try:
            def callback(data):
                if data is not None:
                    self._data = self._currentData
                    self.isSave = True
                else:
                    from src.common.widget.notifications.notify import Notifications
                    Notifications(parent=main_controller.list_parent['CameraScreen'], title=self.tr('Failed to save the grid'), icon=Style.PrimaryImage.fail_result)
            logger.info(f'self._currentData = {self._currentData}')
            if self.controller is None:
                self.controller = self.get_controller()
            subThread = SubThread(parent=self, target=self.controller.update_tabmodel_by_put, args=(self._currentData,),callback=callback)
            subThread.start()
        except Exception as e:
            logger.info(f'shortcut_activated error: {e}')


    def load_saved_view(self):
        """Load cameras from saved view data

        This method loads cameras from the saved view data in the tab model.
        It parses the listGridData string and adds cameras to the grid.
        """
        logger.debug(f'🔍🔍 LOAD_SAVED_VIEW STARTED')

        try:
            # 1. Lấy thông tin grid size từ currentGrid
            if self.get_property("currentGrid",None):
                grid_config_str = self.get_property("currentGrid")
                logger.debug(f"🔍🔍 Grid configuration: {grid_config_str}")

                # Phân tích chuỗi JSON để lấy row và col
                if isinstance(grid_config_str, str):
                    try:
                        grid_config = json.loads(grid_config_str.replace("'", '"'))
                        if 'row' in grid_config and 'col' in grid_config:
                            # Cập nhật kích thước lưới
                            columns = int(grid_config['col'])
                            rows = int(grid_config['row'])

                            # Đảm bảo kích thước lưới nằm trong giới hạn hợp lý
                            max_columns = 12
                            max_rows = 12
                            columns = min(columns, max_columns)
                            rows = min(rows, max_rows)

                            # Cập nhật kích thước lưới
                            self._previous_columns = columns
                            self._previous_rows = rows
                            self._columns = columns
                            self._rows = rows

                            # Phát tín hiệu thay đổi kích thước lưới
                            self.columnsChanged.emit(self._columns)
                            self.rowsChanged.emit(self._rows)
                            logger.debug(f"🔍🔍 Set grid size to {columns}x{rows}")
                    except Exception as e:
                        logger.error(f"🔍🔍 Error parsing grid configuration: {e}")

            # 2. Lấy thông tin camera từ listGridData
            listGridData = {}
            logger.debug(f"🔍🔍 Attempting to get listGridData from tab model")

            # Lấy dữ liệu từ tab model
            grid_data_str = self.get_property('listGridData')
            logger.debug(f"🔍🔍 Grid data: {grid_data_str}")

            # Phân tích chuỗi JSON để lấy thông tin camera
            if isinstance(grid_data_str, str):
                logger.debug(f"🔍🔍 Grid data is string, attempting to parse")
                try:
                    # Thử phân tích bằng json.loads
                    listGridData = json.loads(grid_data_str.replace("'", '"'))
                    logger.debug(f"🔍🔍 Successfully parsed grid data using json.loads")
                except Exception as json_error:
                    logger.error(f"🔍🔍 JSON parse error: {json_error}")
                    try:
                        # Thử phân tích bằng ast.literal_eval
                        listGridData = ast.literal_eval(grid_data_str)
                        logger.debug(f"🔍🔍 Successfully parsed grid data using ast.literal_eval")
                    except Exception as ast_error:
                        logger.error(f"🔍🔍 AST parse error: {ast_error}")
                        # Thử một cách khác - tìm kiếm pattern trong chuỗi
                        logger.debug(f"🔍🔍 Attempting regex pattern matching")
                        try:
                            # Pattern cho camera_id, position, width, height
                            import re
                            pattern = r"(\d+):\s*{[^}]*'camera_id':\s*'([^']+)'[^}]*'width':\s*(\d+)[^}]*'height':\s*(\d+)"
                            matches = re.findall(pattern, grid_data_str)

                            if matches:
                                logger.debug(f"🔍🔍 Found {len(matches)} camera entries using regex")
                                # Tạo listGridData từ các matches
                                for match in matches:
                                    position = int(match[0])
                                    camera_id = match[1]
                                    width = int(match[2])
                                    height = int(match[3])

                                    listGridData[position] = {
                                        'type': 'Camera',
                                        'position': position,
                                        'camera_id': camera_id,
                                        'width': width,
                                        'height': height
                                    }
                                logger.debug(f"🔍🔍 Created grid data with {len(listGridData)} cameras")
                        except Exception as regex_error:
                            logger.error(f"🔍🔍 Regex error: {regex_error}")
            # Nếu grid_data_str đã là dictionary
            elif isinstance(grid_data_str, dict):
                logger.debug(f"🔍🔍 Grid data is already a dictionary with {len(grid_data_str)} items")
                listGridData = grid_data_str

            # 3. Thêm camera vào lưới
            if listGridData:
                logger.debug(f"🔍🔍 Adding {len(listGridData)} cameras to grid")

                # Xóa camera hiện tại
                self._active_cells = {}
                self._cell_dimensions = {}

                # Track which positions have already been processed to avoid duplicates
                processed_positions = set()

                # Thêm camera mới
                for key_str, camera_data in listGridData.items():
                    try:
                        # Lấy vị trí
                        key = int(key_str) if isinstance(key_str, str) else key_str

                        # Skip if this position is part of a multi-cell camera that we've already processed
                        if key in processed_positions:
                            logger.debug(f"🔍🔍 Skipping position {key} as it's already processed")
                            continue

                        # Lấy camera ID
                        camera_id = camera_data.get('camera_id')
                        if not camera_id:
                            logger.warning(f"🔍🔍 No camera ID for position {key}")
                            continue

                        # Lấy kích thước camera
                        cell_dims = {
                            "width": camera_data.get('width', 1),
                            "height": camera_data.get('height', 1)
                        }

                        # Lấy camera model
                        camera_model = camera_model_manager.get_camera_model(id=camera_id)
                        if not camera_model:
                            logger.warning(f"🔍🔍 Camera model not found for ID: {camera_id}")
                            continue

                        logger.debug(f"🔍🔍 Adding camera {camera_id} at position {key} with size {cell_dims['width']}x{cell_dims['height']}")

                        # Thêm camera vào _active_cells
                        self._active_cells[key] = camera_model

                        # Đặt kích thước camera
                        self._cell_dimensions[key] = cell_dims

                        # Mark all cells occupied by this camera as processed
                        start_row = key // self._columns
                        start_col = key % self._columns
                        for row in range(start_row, start_row + cell_dims["height"]):
                            for col in range(start_col, start_col + cell_dims["width"]):
                                occupied_pos = row * self._columns + col
                                if occupied_pos in self._active_cells and self._active_cells[occupied_pos] == camera_model:
                                    processed_positions.add(occupied_pos) # Đánh dấu ô đã được xử lý
                                elif occupied_pos != key:
                                    # Đánh dấu các ô bị chiếm bởi camera nhiều ô
                                    self._active_cells[occupied_pos] = camera_model
                        camera_model = camera_model_manager.get_camera_model(id = camera_id)
                        # Phát tín hiệu thêm camera
                        self.videoInfoChanged.emit(key, camera_model, True,False)

                        # Phát tín hiệu thay đổi kích thước camera
                        self.cellDimensionsChanged.emit(key, cell_dims["width"], cell_dims["height"])

                    except Exception as e:
                        logger.error(f"🔍🔍 Error adding camera at position {key_str}: {e}")
                        logger.error(traceback.format_exc())

                # Cập nhật next_position
                self._next_position = self._calculate_next_position()

                # Cập nhật layout
                self._updateLayout()

                # Đặt isSave = True khi mở một saved view
                self.isSave = True
                logger.debug(f"🔍🔍 Successfully loaded {len(listGridData)} cameras from saved view, set isSave = True")
                return True
            else:
                logger.warning("🔍🔍 No cameras to add from saved view")
                return False

        except Exception as e:
            logger.error(f"🔍🔍 Error loading saved view: {e}")
            logger.error(traceback.format_exc())
            return False


class GridManager(QObject):
    addGridModelChanged = Signal(tuple)
    addGridModelListChanged = Signal(tuple)
    __instance = None
    def __init__(self):
        super().__init__()
        self.data = {}
        self.addGridModelListChanged.connect(self.addGridModelList)
        self.addGridModelChanged.connect(self.addGridModel)

    @staticmethod
    def get_instance():
        if GridManager.__instance is None:
            GridManager.__instance = GridManager()
        return GridManager.__instance

    def addGridModel(self, data = None):
        gridModel:GridModel = data
        self.data[gridModel.get_property('id')] = gridModel

    def addGridModelList(self,data = None):
        controller, gridModelList = data
        for item in gridModelList:
            gridModel = GridModel(data=item)
            gridModel.set_property("isShow", False)
            self.data[gridModel.get_property('id')] = gridModel
        # self.add_tab_model_list_signal.emit((controller,tab_model_list))

    def getGridModel(self, id = None):
        if id in self.data:
            return self.data[id]
        return None

    def removeGridModel(self, gridModel = None):
        if gridModel is None:
            logger.error("GridModel is None")
            return
        else:
            if gridModel.get_property('id') in self.data:
                del self.data[gridModel.get_property('id')]

    def __repr__(self):
        # repr_data = {
        #     k: v._data if hasattr(v, "get_property") else str(v)
        #     for k, v in self.data.items()
        # }
        # return f"<GridManager data={repr_data}>"
        repr_lines = ["<GridManager data>:"]
        for key, value in self.data.items():
            line = f"   {key}: {value._data}"
            # repr_lines.append(f"****************** Tab name = {value.get_property("name",None)} tabType = {value.get_property("type",None)} **********************")
            repr_lines.append(line)

        return "\n".join(repr_lines)

gridManager = GridManager.get_instance()
